/* Enhanced Insurance Dashboard Styles */
.dashboard-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: 'Roboto', sans-serif;
}

/* Welcome Header */
.welcome-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-content h1 {
  margin: 0 0 8px 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.welcome-content p {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.quick-stats {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.stat-item mat-icon {
  font-size: 18px;
}

.welcome-actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  min-width: 120px;
  height: 48px;
  border-radius: 24px;
  font-weight: 500;
  text-transform: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Section Titles */
.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 24px 0;
  font-size: 1.5rem;
  font-weight: 500;
  color: #333;
}

.section-title mat-icon {
  color: #667eea;
}

/* KPI Section */
.kpi-section {
  margin-bottom: 40px;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.kpi-card {
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.kpi-card.revenue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.kpi-card.policies {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.kpi-card.claims {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.kpi-card.customers {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.kpi-header mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
}

.kpi-trend {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.9rem;
  font-weight: 500;
}

.kpi-trend.up {
  background: rgba(76, 175, 80, 0.2);
}

.kpi-trend.down {
  background: rgba(244, 67, 54, 0.2);
}

.kpi-card h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 400;
  opacity: 0.9;
}

.kpi-value {
  margin: 0 0 4px 0;
  font-size: 2.2rem;
  font-weight: 700;
}

.kpi-subtitle {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Quick Actions */
.quick-actions-section {
  margin-bottom: 40px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.quick-action-card {
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.quick-action-card mat-card-content {
  text-align: center;
  padding: 24px;
}

.quick-action-card mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.quick-action-card h3 {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: 500;
  color: #333;
}

.quick-action-card p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Modules Section */
.modules-section {
  margin-bottom: 40px;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.module-card {
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.module-card mat-card-header {
  background: #f8f9fa;
  padding: 20px;
}

.module-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 500;
}

.module-card mat-card-content {
  padding: 20px;
}

.module-stats {
  display: flex;
  gap: 20px;
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.module-btn {
  width: 100%;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  text-transform: none;
}

/* Activities Section */
.activities-section {
  margin-bottom: 40px;
}

.activities-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.activity-content {
  flex: 1;
}

.activity-content h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.activity-content p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.activity-time {
  font-size: 0.8rem;
  color: #999;
  font-weight: 500;
}

/* Status Section */
.status-section {
  margin-bottom: 40px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.status-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
}

.status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.status-header mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.status-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

.status-value {
  margin: 0 0 16px 0;
  font-size: 1rem;
  color: #666;
}

.status-indicator {
  width: 100%;
  height: 4px;
  border-radius: 2px;
}

.status-indicator.green {
  background: linear-gradient(90deg, #4caf50, #8bc34a);
}

.status-indicator.blue {
  background: linear-gradient(90deg, #2196f3, #03a9f4);
}

.status-indicator.orange {
  background: linear-gradient(90deg, #ff9800, #ffc107);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .welcome-header {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .quick-stats {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .welcome-header {
    padding: 24px;
  }

  .welcome-content h1 {
    font-size: 2rem;
  }

  .quick-stats {
    flex-direction: column;
    gap: 12px;
  }

  .stat-item {
    justify-content: center;
  }

  .welcome-actions {
    flex-direction: column;
    width: 100%;
  }

  .action-btn {
    width: 100%;
  }

  .kpi-grid,
  .quick-actions-grid,
  .modules-grid,
  .status-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 1.3rem;
  }

  .kpi-value {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 12px;
  }

  .welcome-header {
    padding: 20px;
  }

  .welcome-content h1 {
    font-size: 1.8rem;
  }

  .kpi-card mat-card-content,
  .quick-action-card mat-card-content,
  .module-card mat-card-content {
    padding: 16px;
  }

  .activity-item {
    padding: 12px 0;
  }

  .activity-icon {
    width: 40px;
    height: 40px;
  }

  .activity-icon mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

/* Animation for smooth loading */
.dashboard-container > * {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar for activities */
.activity-list::-webkit-scrollbar {
  width: 6px;
}

.activity-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
