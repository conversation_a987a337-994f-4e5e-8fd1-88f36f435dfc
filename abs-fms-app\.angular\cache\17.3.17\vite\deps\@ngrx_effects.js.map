{"version": 3, "sources": ["../../../../../node_modules/@ngrx/operators/fesm2022/ngrx-operators.mjs", "../../../../../node_modules/@ngrx/effects/fesm2022/ngrx-effects.mjs"], "sourcesContent": ["import { of, EMPTY } from 'rxjs';\nimport { concatMap, withLatestFrom, tap, catchError, finalize } from 'rxjs/operators';\n\n/**\n * `concatLatestFrom` combines the source value\n * and the last available value from a lazily evaluated Observable\n * in a new array\n *\n * @usageNotes\n *\n * Select the active customer from the NgRx Store\n *\n * ```ts\n * import { concatLatestFrom } from '@ngrx/effects';\n * import * as fromCustomers from '../customers';\n *\n * this.actions$.pipe(\n *  concatLatestFrom(() => this.store.select(fromCustomers.selectActiveCustomer))\n * )\n * ```\n *\n * Select a customer from the NgRx Store by its id that is available on the action\n *\n * ```ts\n * import { concatLatestFrom } from '@ngrx/effects';\n * import * fromCustomers from '../customers';\n *\n * this.actions$.pipe(\n *  concatLatestFrom((action) => this.store.select(fromCustomers.selectCustomer(action.customerId)))\n * )\n * ```\n */\nfunction concatLatestFrom(observablesFactory) {\n    return concatMap((value) => {\n        const observables = observablesFactory(value);\n        const observablesAsArray = Array.isArray(observables)\n            ? observables\n            : [observables];\n        return of(value).pipe(withLatestFrom(...observablesAsArray));\n    });\n}\n\n/**\n * Handles the response in ComponentStore effects in a safe way, without\n * additional boilerplate. It enforces that the error case is handled and\n * that the effect would still be running should an error occur.\n *\n * Takes optional callbacks for `complete` and `finalize`.\n *\n * @usageNotes\n *\n * ```ts\n * readonly dismissAlert = this.effect<Alert>((alert$) => {\n *   return alert$.pipe(\n *     concatMap(\n *       (alert) => this.alertsService.dismissAlert(alert).pipe(\n *         tapResponse(\n *           (dismissedAlert) => this.alertDismissed(dismissedAlert),\n *           (error: { message: string }) => this.logError(error.message)\n *         )\n *       )\n *     )\n *   );\n * });\n *\n * readonly loadUsers = this.effect<void>((trigger$) => {\n *   return trigger$.pipe(\n *     tap(() => this.patchState({ loading: true })),\n *     exhaustMap(() =>\n *       this.usersService.getAll().pipe(\n *         tapResponse({\n *           next: (users) => this.patchState({ users }),\n *           error: (error: HttpErrorResponse) => this.logError(error.message),\n *           finalize: () => this.patchState({ loading: false }),\n *         })\n *       )\n *     )\n *   );\n * });\n * ```\n */\nfunction tapResponse(observerOrNext, error, complete) {\n    const observer = typeof observerOrNext === 'function'\n        ? {\n            next: observerOrNext,\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            error: error,\n            complete,\n        }\n        : observerOrNext;\n    return (source) => source.pipe(tap({ next: observer.next, complete: observer.complete }), catchError((error) => {\n        observer.error(error);\n        return EMPTY;\n    }), observer.finalize ? finalize(observer.finalize) : (source$) => source$);\n}\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { concatLatestFrom, tapResponse };\n\n", "import * as operators from '@ngrx/operators';\nimport * as i1 from 'rxjs';\nimport { merge, Observable, Subject, defer } from 'rxjs';\nimport { ignoreElements, materialize, map, catchError, filter, groupBy, mergeMap, exhaustMap, dematerialize, take, concatMap, finalize } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, NgModule, Optional, inject, makeEnvironmentProviders, ENVIRONMENT_INITIALIZER } from '@angular/core';\nimport * as i3 from '@ngrx/store';\nimport { ScannedActionsSubject, createAction, ROOT_STORE_PROVIDER, FEATURE_STATE_PROVIDER, Store } from '@ngrx/store';\nconst DEFAULT_EFFECT_CONFIG = {\n  dispatch: true,\n  functional: false,\n  useEffectsErrorHandler: true\n};\nconst CREATE_EFFECT_METADATA_KEY = '__@ngrx/effects_create__';\n\n/**\n * @description\n *\n * Creates an effect from a source and an `EffectConfig`.\n *\n * @param source A function which returns an observable or observable factory.\n * @param config A `EffectConfig` to configure the effect. By default,\n * `dispatch` is true, `functional` is false, and `useEffectsErrorHandler` is\n * true.\n * @returns If `EffectConfig`#`functional` is true, returns the source function.\n * Else, returns the source function result. When `EffectConfig`#`dispatch` is\n * true, the source function result needs to be `Observable<Action>`.\n *\n * @usageNotes\n *\n * ### Class Effects\n *\n * ```ts\n * @Injectable()\n * export class FeatureEffects {\n *   // mapping to a different action\n *   readonly effect1$ = createEffect(\n *     () => this.actions$.pipe(\n *       ofType(FeatureActions.actionOne),\n *       map(() => FeatureActions.actionTwo())\n *     )\n *   );\n *\n *   // non-dispatching effect\n *   readonly effect2$ = createEffect(\n *     () => this.actions$.pipe(\n *       ofType(FeatureActions.actionTwo),\n *       tap(() => console.log('Action Two Dispatched'))\n *     ),\n *     { dispatch: false } // FeatureActions.actionTwo is not dispatched\n *   );\n *\n *   constructor(private readonly actions$: Actions) {}\n * }\n * ```\n *\n * ### Functional Effects\n *\n * ```ts\n * // mapping to a different action\n * export const loadUsers = createEffect(\n *   (actions$ = inject(Actions), usersService = inject(UsersService)) => {\n *     return actions$.pipe(\n *       ofType(UsersPageActions.opened),\n *       exhaustMap(() => {\n *         return usersService.getAll().pipe(\n *           map((users) => UsersApiActions.usersLoadedSuccess({ users })),\n *           catchError((error) =>\n *             of(UsersApiActions.usersLoadedFailure({ error }))\n *           )\n *         );\n *       })\n *     );\n *   },\n *   { functional: true }\n * );\n *\n * // non-dispatching functional effect\n * export const logDispatchedActions = createEffect(\n *   () => inject(Actions).pipe(tap(console.log)),\n *   { functional: true, dispatch: false }\n * );\n * ```\n */\nfunction createEffect(source, config = {}) {\n  const effect = config.functional ? source : source();\n  const value = {\n    ...DEFAULT_EFFECT_CONFIG,\n    ...config // Overrides any defaults if values are provided\n  };\n  Object.defineProperty(effect, CREATE_EFFECT_METADATA_KEY, {\n    value\n  });\n  return effect;\n}\nfunction getCreateEffectMetadata(instance) {\n  const propertyNames = Object.getOwnPropertyNames(instance);\n  const metadata = propertyNames.filter(propertyName => {\n    if (instance[propertyName] && instance[propertyName].hasOwnProperty(CREATE_EFFECT_METADATA_KEY)) {\n      // If the property type has overridden `hasOwnProperty` we need to ensure\n      // that the metadata is valid (containing a `dispatch` property)\n      // https://github.com/ngrx/platform/issues/2975\n      const property = instance[propertyName];\n      return property[CREATE_EFFECT_METADATA_KEY].hasOwnProperty('dispatch');\n    }\n    return false;\n  }).map(propertyName => {\n    const metaData = instance[propertyName][CREATE_EFFECT_METADATA_KEY];\n    return {\n      propertyName,\n      ...metaData\n    };\n  });\n  return metadata;\n}\nfunction getEffectsMetadata(instance) {\n  return getSourceMetadata(instance).reduce((acc, {\n    propertyName,\n    dispatch,\n    useEffectsErrorHandler\n  }) => {\n    acc[propertyName] = {\n      dispatch,\n      useEffectsErrorHandler\n    };\n    return acc;\n  }, {});\n}\nfunction getSourceMetadata(instance) {\n  return getCreateEffectMetadata(instance);\n}\nfunction getSourceForInstance(instance) {\n  return Object.getPrototypeOf(instance);\n}\nfunction isClassInstance(obj) {\n  return !!obj.constructor && obj.constructor.name !== 'Object' && obj.constructor.name !== 'Function';\n}\nfunction isClass(classOrRecord) {\n  return typeof classOrRecord === 'function';\n}\nfunction getClasses(classesAndRecords) {\n  return classesAndRecords.filter(isClass);\n}\nfunction isToken(tokenOrRecord) {\n  return tokenOrRecord instanceof InjectionToken || isClass(tokenOrRecord);\n}\nfunction mergeEffects(sourceInstance, globalErrorHandler, effectsErrorHandler) {\n  const source = getSourceForInstance(sourceInstance);\n  const isClassBasedEffect = !!source && source.constructor.name !== 'Object';\n  const sourceName = isClassBasedEffect ? source.constructor.name : null;\n  const observables$ = getSourceMetadata(sourceInstance).map(({\n    propertyName,\n    dispatch,\n    useEffectsErrorHandler\n  }) => {\n    const observable$ = typeof sourceInstance[propertyName] === 'function' ? sourceInstance[propertyName]() : sourceInstance[propertyName];\n    const effectAction$ = useEffectsErrorHandler ? effectsErrorHandler(observable$, globalErrorHandler) : observable$;\n    if (dispatch === false) {\n      return effectAction$.pipe(ignoreElements());\n    }\n    const materialized$ = effectAction$.pipe(materialize());\n    return materialized$.pipe(map(notification => ({\n      effect: sourceInstance[propertyName],\n      notification,\n      propertyName,\n      sourceName,\n      sourceInstance\n    })));\n  });\n  return merge(...observables$);\n}\nconst MAX_NUMBER_OF_RETRY_ATTEMPTS = 10;\nfunction defaultEffectsErrorHandler(observable$, errorHandler, retryAttemptLeft = MAX_NUMBER_OF_RETRY_ATTEMPTS) {\n  return observable$.pipe(catchError(error => {\n    if (errorHandler) errorHandler.handleError(error);\n    if (retryAttemptLeft <= 1) {\n      return observable$; // last attempt\n    }\n    // Return observable that produces this particular effect\n    return defaultEffectsErrorHandler(observable$, errorHandler, retryAttemptLeft - 1);\n  }));\n}\nclass Actions extends Observable {\n  constructor(source) {\n    super();\n    if (source) {\n      this.source = source;\n    }\n  }\n  lift(operator) {\n    const observable = new Actions();\n    observable.source = this;\n    observable.operator = operator;\n    return observable;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function Actions_Factory(t) {\n      return new (t || Actions)(i0.ɵɵinject(ScannedActionsSubject));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Actions,\n      factory: Actions.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Actions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Observable,\n    decorators: [{\n      type: Inject,\n      args: [ScannedActionsSubject]\n    }]\n  }], null);\n})();\n/**\n * `ofType` filters an Observable of `Actions` into an Observable of the actions\n * whose type strings are passed to it.\n *\n * For example, if `actions` has type `Actions<AdditionAction|SubstractionAction>`, and\n * the type of the `Addition` action is `add`, then\n * `actions.pipe(ofType('add'))` returns an `Observable<AdditionAction>`.\n *\n * Properly typing this function is hard and requires some advanced TS tricks\n * below.\n *\n * Type narrowing automatically works, as long as your `actions` object\n * starts with a `Actions<SomeUnionOfActions>` instead of generic `Actions`.\n *\n * For backwards compatibility, when one passes a single type argument\n * `ofType<T>('something')` the result is an `Observable<T>`. Note, that `T`\n * completely overrides any possible inference from 'something'.\n *\n * Unfortunately, for unknown 'actions: Actions' these types will produce\n * 'Observable<never>'. In such cases one has to manually set the generic type\n * like `actions.ofType<AdditionAction>('add')`.\n *\n * @usageNotes\n *\n * Filter the Actions stream on the \"customers page loaded\" action\n *\n * ```ts\n * import { ofType } from '@ngrx/effects';\n * import * fromCustomers from '../customers';\n *\n * this.actions$.pipe(\n *  ofType(fromCustomers.pageLoaded)\n * )\n * ```\n */\nfunction ofType(...allowedTypes) {\n  return filter(action => allowedTypes.some(typeOrActionCreator => {\n    if (typeof typeOrActionCreator === 'string') {\n      // Comparing the string to type\n      return typeOrActionCreator === action.type;\n    }\n    // We are filtering by ActionCreator\n    return typeOrActionCreator.type === action.type;\n  }));\n}\nconst _ROOT_EFFECTS_GUARD = new InjectionToken('@ngrx/effects Internal Root Guard');\nconst USER_PROVIDED_EFFECTS = new InjectionToken('@ngrx/effects User Provided Effects');\nconst _ROOT_EFFECTS = new InjectionToken('@ngrx/effects Internal Root Effects');\nconst _ROOT_EFFECTS_INSTANCES = new InjectionToken('@ngrx/effects Internal Root Effects Instances');\nconst _FEATURE_EFFECTS = new InjectionToken('@ngrx/effects Internal Feature Effects');\nconst _FEATURE_EFFECTS_INSTANCE_GROUPS = new InjectionToken('@ngrx/effects Internal Feature Effects Instance Groups');\nconst EFFECTS_ERROR_HANDLER = new InjectionToken('@ngrx/effects Effects Error Handler', {\n  providedIn: 'root',\n  factory: () => defaultEffectsErrorHandler\n});\nconst ROOT_EFFECTS_INIT = '@ngrx/effects/init';\nconst rootEffectsInit = createAction(ROOT_EFFECTS_INIT);\nfunction reportInvalidActions(output, reporter) {\n  if (output.notification.kind === 'N') {\n    const action = output.notification.value;\n    const isInvalidAction = !isAction(action);\n    if (isInvalidAction) {\n      reporter.handleError(new Error(`Effect ${getEffectName(output)} dispatched an invalid action: ${stringify(action)}`));\n    }\n  }\n}\nfunction isAction(action) {\n  return typeof action !== 'function' && action && action.type && typeof action.type === 'string';\n}\nfunction getEffectName({\n  propertyName,\n  sourceInstance,\n  sourceName\n}) {\n  const isMethod = typeof sourceInstance[propertyName] === 'function';\n  const isClassBasedEffect = !!sourceName;\n  return isClassBasedEffect ? `\"${sourceName}.${String(propertyName)}${isMethod ? '()' : ''}\"` : `\"${String(propertyName)}()\"`;\n}\nfunction stringify(action) {\n  try {\n    return JSON.stringify(action);\n  } catch {\n    return action;\n  }\n}\nconst onIdentifyEffectsKey = 'ngrxOnIdentifyEffects';\nfunction isOnIdentifyEffects(instance) {\n  return isFunction(instance, onIdentifyEffectsKey);\n}\nconst onRunEffectsKey = 'ngrxOnRunEffects';\nfunction isOnRunEffects(instance) {\n  return isFunction(instance, onRunEffectsKey);\n}\nconst onInitEffects = 'ngrxOnInitEffects';\nfunction isOnInitEffects(instance) {\n  return isFunction(instance, onInitEffects);\n}\nfunction isFunction(instance, functionName) {\n  return instance && functionName in instance && typeof instance[functionName] === 'function';\n}\nclass EffectSources extends Subject {\n  constructor(errorHandler, effectsErrorHandler) {\n    super();\n    this.errorHandler = errorHandler;\n    this.effectsErrorHandler = effectsErrorHandler;\n  }\n  addEffects(effectSourceInstance) {\n    this.next(effectSourceInstance);\n  }\n  /**\n   * @internal\n   */\n  toActions() {\n    return this.pipe(groupBy(effectsInstance => isClassInstance(effectsInstance) ? getSourceForInstance(effectsInstance) : effectsInstance), mergeMap(source$ => {\n      return source$.pipe(groupBy(effectsInstance));\n    }), mergeMap(source$ => {\n      const effect$ = source$.pipe(exhaustMap(sourceInstance => {\n        return resolveEffectSource(this.errorHandler, this.effectsErrorHandler)(sourceInstance);\n      }), map(output => {\n        reportInvalidActions(output, this.errorHandler);\n        return output.notification;\n      }), filter(notification => notification.kind === 'N' && notification.value != null), dematerialize());\n      // start the stream with an INIT action\n      // do this only for the first Effect instance\n      const init$ = source$.pipe(take(1), filter(isOnInitEffects), map(instance => instance.ngrxOnInitEffects()));\n      return merge(effect$, init$);\n    }));\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectSources_Factory(t) {\n      return new (t || EffectSources)(i0.ɵɵinject(i0.ErrorHandler), i0.ɵɵinject(EFFECTS_ERROR_HANDLER));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EffectSources,\n      factory: EffectSources.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectSources, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.ErrorHandler\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [EFFECTS_ERROR_HANDLER]\n    }]\n  }], null);\n})();\nfunction effectsInstance(sourceInstance) {\n  if (isOnIdentifyEffects(sourceInstance)) {\n    return sourceInstance.ngrxOnIdentifyEffects();\n  }\n  return '';\n}\nfunction resolveEffectSource(errorHandler, effectsErrorHandler) {\n  return sourceInstance => {\n    const mergedEffects$ = mergeEffects(sourceInstance, errorHandler, effectsErrorHandler);\n    if (isOnRunEffects(sourceInstance)) {\n      return sourceInstance.ngrxOnRunEffects(mergedEffects$);\n    }\n    return mergedEffects$;\n  };\n}\nclass EffectsRunner {\n  get isStarted() {\n    return !!this.effectsSubscription;\n  }\n  constructor(effectSources, store) {\n    this.effectSources = effectSources;\n    this.store = store;\n    this.effectsSubscription = null;\n  }\n  start() {\n    if (!this.effectsSubscription) {\n      this.effectsSubscription = this.effectSources.toActions().subscribe(this.store);\n    }\n  }\n  ngOnDestroy() {\n    if (this.effectsSubscription) {\n      this.effectsSubscription.unsubscribe();\n      this.effectsSubscription = null;\n    }\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectsRunner_Factory(t) {\n      return new (t || EffectsRunner)(i0.ɵɵinject(EffectSources), i0.ɵɵinject(i3.Store));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EffectsRunner,\n      factory: EffectsRunner.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectsRunner, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: EffectSources\n  }, {\n    type: i3.Store\n  }], null);\n})();\nclass EffectsRootModule {\n  constructor(sources, runner, store, rootEffectsInstances, storeRootModule, storeFeatureModule, guard) {\n    this.sources = sources;\n    runner.start();\n    for (const effectsInstance of rootEffectsInstances) {\n      sources.addEffects(effectsInstance);\n    }\n    store.dispatch({\n      type: ROOT_EFFECTS_INIT\n    });\n  }\n  addEffects(effectsInstance) {\n    this.sources.addEffects(effectsInstance);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectsRootModule_Factory(t) {\n      return new (t || EffectsRootModule)(i0.ɵɵinject(EffectSources), i0.ɵɵinject(EffectsRunner), i0.ɵɵinject(i3.Store), i0.ɵɵinject(_ROOT_EFFECTS_INSTANCES), i0.ɵɵinject(i3.StoreRootModule, 8), i0.ɵɵinject(i3.StoreFeatureModule, 8), i0.ɵɵinject(_ROOT_EFFECTS_GUARD, 8));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: EffectsRootModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectsRootModule, [{\n    type: NgModule,\n    args: [{}]\n  }], () => [{\n    type: EffectSources\n  }, {\n    type: EffectsRunner\n  }, {\n    type: i3.Store\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [_ROOT_EFFECTS_INSTANCES]\n    }]\n  }, {\n    type: i3.StoreRootModule,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.StoreFeatureModule,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [_ROOT_EFFECTS_GUARD]\n    }]\n  }], null);\n})();\nclass EffectsFeatureModule {\n  constructor(effectsRootModule, effectsInstanceGroups, storeRootModule, storeFeatureModule) {\n    const effectsInstances = effectsInstanceGroups.flat();\n    for (const effectsInstance of effectsInstances) {\n      effectsRootModule.addEffects(effectsInstance);\n    }\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectsFeatureModule_Factory(t) {\n      return new (t || EffectsFeatureModule)(i0.ɵɵinject(EffectsRootModule), i0.ɵɵinject(_FEATURE_EFFECTS_INSTANCE_GROUPS), i0.ɵɵinject(i3.StoreRootModule, 8), i0.ɵɵinject(i3.StoreFeatureModule, 8));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: EffectsFeatureModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectsFeatureModule, [{\n    type: NgModule,\n    args: [{}]\n  }], () => [{\n    type: EffectsRootModule\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [_FEATURE_EFFECTS_INSTANCE_GROUPS]\n    }]\n  }, {\n    type: i3.StoreRootModule,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.StoreFeatureModule,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\nclass EffectsModule {\n  static forFeature(...featureEffects) {\n    const effects = featureEffects.flat();\n    const effectsClasses = getClasses(effects);\n    return {\n      ngModule: EffectsFeatureModule,\n      providers: [effectsClasses, {\n        provide: _FEATURE_EFFECTS,\n        multi: true,\n        useValue: effects\n      }, {\n        provide: USER_PROVIDED_EFFECTS,\n        multi: true,\n        useValue: []\n      }, {\n        provide: _FEATURE_EFFECTS_INSTANCE_GROUPS,\n        multi: true,\n        useFactory: createEffectsInstances,\n        deps: [_FEATURE_EFFECTS, USER_PROVIDED_EFFECTS]\n      }]\n    };\n  }\n  static forRoot(...rootEffects) {\n    const effects = rootEffects.flat();\n    const effectsClasses = getClasses(effects);\n    return {\n      ngModule: EffectsRootModule,\n      providers: [effectsClasses, {\n        provide: _ROOT_EFFECTS,\n        useValue: [effects]\n      }, {\n        provide: _ROOT_EFFECTS_GUARD,\n        useFactory: _provideForRootGuard\n      }, {\n        provide: USER_PROVIDED_EFFECTS,\n        multi: true,\n        useValue: []\n      }, {\n        provide: _ROOT_EFFECTS_INSTANCES,\n        useFactory: createEffectsInstances,\n        deps: [_ROOT_EFFECTS, USER_PROVIDED_EFFECTS]\n      }]\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function EffectsModule_Factory(t) {\n      return new (t || EffectsModule)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: EffectsModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EffectsModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\nfunction createEffectsInstances(effectsGroups, userProvidedEffectsGroups) {\n  const effects = [];\n  for (const effectsGroup of effectsGroups) {\n    effects.push(...effectsGroup);\n  }\n  for (const userProvidedEffectsGroup of userProvidedEffectsGroups) {\n    effects.push(...userProvidedEffectsGroup);\n  }\n  return effects.map(effectsTokenOrRecord => isToken(effectsTokenOrRecord) ? inject(effectsTokenOrRecord) : effectsTokenOrRecord);\n}\nfunction _provideForRootGuard() {\n  const runner = inject(EffectsRunner, {\n    optional: true,\n    skipSelf: true\n  });\n  const rootEffects = inject(_ROOT_EFFECTS, {\n    self: true\n  });\n  // check whether any effects are actually passed\n  const hasEffects = !(rootEffects.length === 1 && rootEffects[0].length === 0);\n  if (hasEffects && runner) {\n    throw new TypeError(`EffectsModule.forRoot() called twice. Feature modules should use EffectsModule.forFeature() instead.`);\n  }\n  return 'guarded';\n}\n\n/**\n * Wraps project fn with error handling making it safe to use in Effects.\n * Takes either a config with named properties that represent different possible\n * callbacks or project/error callbacks that are required.\n */\nfunction act(/** Allow to take either config object or project/error functions */\nconfigOrProject, errorFn) {\n  const {\n    project,\n    error,\n    complete,\n    operator,\n    unsubscribe\n  } = typeof configOrProject === 'function' ? {\n    project: configOrProject,\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    error: errorFn,\n    operator: concatMap,\n    complete: undefined,\n    unsubscribe: undefined\n  } : {\n    ...configOrProject,\n    operator: configOrProject.operator || concatMap\n  };\n  return source => defer(() => {\n    const subject = new Subject();\n    return merge(source.pipe(operator((input, index) => defer(() => {\n      let completed = false;\n      let errored = false;\n      let projectedCount = 0;\n      return project(input, index).pipe(materialize(), map(notification => {\n        switch (notification.kind) {\n          case 'E':\n            errored = true;\n            return {\n              kind: 'N',\n              value: error(notification.error, input)\n            };\n          case 'C':\n            completed = true;\n            return complete ? {\n              kind: 'N',\n              value: complete(projectedCount, input)\n            } : undefined;\n          default:\n            ++projectedCount;\n            return notification;\n        }\n      }), filter(n => n != null), dematerialize(), finalize(() => {\n        if (!completed && !errored && unsubscribe) {\n          subject.next(unsubscribe(projectedCount, input));\n        }\n      }));\n    }))), subject);\n  });\n}\n\n/**\n * @usageNotes\n *\n * ### Providing effects at the root level\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideEffects(RouterEffects)],\n * });\n * ```\n *\n * ### Providing effects at the feature level\n *\n * ```ts\n * const booksRoutes: Route[] = [\n *   {\n *     path: '',\n *     providers: [provideEffects(BooksApiEffects)],\n *     children: [\n *       { path: '', component: BookListComponent },\n *       { path: ':id', component: BookDetailsComponent },\n *     ],\n *   },\n * ];\n * ```\n */\nfunction provideEffects(...effects) {\n  const effectsClassesAndRecords = effects.flat();\n  const effectsClasses = getClasses(effectsClassesAndRecords);\n  return makeEnvironmentProviders([effectsClasses, {\n    provide: ENVIRONMENT_INITIALIZER,\n    multi: true,\n    useValue: () => {\n      inject(ROOT_STORE_PROVIDER);\n      inject(FEATURE_STATE_PROVIDER, {\n        optional: true\n      });\n      const effectsRunner = inject(EffectsRunner);\n      const effectSources = inject(EffectSources);\n      const shouldInitEffects = !effectsRunner.isStarted;\n      if (shouldInitEffects) {\n        effectsRunner.start();\n      }\n      for (const effectsClassOrRecord of effectsClassesAndRecords) {\n        const effectsInstance = isClass(effectsClassOrRecord) ? inject(effectsClassOrRecord) : effectsClassOrRecord;\n        effectSources.addEffects(effectsInstance);\n      }\n      if (shouldInitEffects) {\n        const store = inject(Store);\n        store.dispatch(rootEffectsInit());\n      }\n    }\n  }]);\n}\n\n/**\n * @deprecated Use `concatLatestFrom` from `@ngrx/operators` instead.\n */\nconst concatLatestFrom = operators.concatLatestFrom;\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Actions, EFFECTS_ERROR_HANDLER, EffectSources, EffectsFeatureModule, EffectsModule, EffectsRootModule, EffectsRunner, ROOT_EFFECTS_INIT, USER_PROVIDED_EFFECTS, act, concatLatestFrom, createEffect, defaultEffectsErrorHandler, getEffectsMetadata, mergeEffects, ofType, provideEffects, rootEffectsInit };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,SAAS,iBAAiB,oBAAoB;AAC1C,SAAO,UAAU,CAAC,UAAU;AACxB,UAAM,cAAc,mBAAmB,KAAK;AAC5C,UAAM,qBAAqB,MAAM,QAAQ,WAAW,IAC9C,cACA,CAAC,WAAW;AAClB,WAAO,GAAG,KAAK,EAAE,KAAK,eAAe,GAAG,kBAAkB,CAAC;AAAA,EAC/D,CAAC;AACL;;;AChCA,IAAM,wBAAwB;AAAA,EAC5B,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,wBAAwB;AAC1B;AACA,IAAM,6BAA6B;AAuEnC,SAAS,aAAa,QAAQ,SAAS,CAAC,GAAG;AACzC,QAAM,SAAS,OAAO,aAAa,SAAS,OAAO;AACnD,QAAM,QAAQ,kCACT,wBACA;AAEL,SAAO,eAAe,QAAQ,4BAA4B;AAAA,IACxD;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,wBAAwB,UAAU;AACzC,QAAM,gBAAgB,OAAO,oBAAoB,QAAQ;AACzD,QAAM,WAAW,cAAc,OAAO,kBAAgB;AACpD,QAAI,SAAS,YAAY,KAAK,SAAS,YAAY,EAAE,eAAe,0BAA0B,GAAG;AAI/F,YAAM,WAAW,SAAS,YAAY;AACtC,aAAO,SAAS,0BAA0B,EAAE,eAAe,UAAU;AAAA,IACvE;AACA,WAAO;AAAA,EACT,CAAC,EAAE,IAAI,kBAAgB;AACrB,UAAM,WAAW,SAAS,YAAY,EAAE,0BAA0B;AAClE,WAAO;AAAA,MACL;AAAA,OACG;AAAA,EAEP,CAAC;AACD,SAAO;AACT;AACA,SAAS,mBAAmB,UAAU;AACpC,SAAO,kBAAkB,QAAQ,EAAE,OAAO,CAAC,KAAK;AAAA,IAC9C;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,YAAY,IAAI;AAAA,MAClB;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,SAAS,kBAAkB,UAAU;AACnC,SAAO,wBAAwB,QAAQ;AACzC;AACA,SAAS,qBAAqB,UAAU;AACtC,SAAO,OAAO,eAAe,QAAQ;AACvC;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,CAAC,CAAC,IAAI,eAAe,IAAI,YAAY,SAAS,YAAY,IAAI,YAAY,SAAS;AAC5F;AACA,SAAS,QAAQ,eAAe;AAC9B,SAAO,OAAO,kBAAkB;AAClC;AACA,SAAS,WAAW,mBAAmB;AACrC,SAAO,kBAAkB,OAAO,OAAO;AACzC;AACA,SAAS,QAAQ,eAAe;AAC9B,SAAO,yBAAyB,kBAAkB,QAAQ,aAAa;AACzE;AACA,SAAS,aAAa,gBAAgB,oBAAoB,qBAAqB;AAC7E,QAAM,SAAS,qBAAqB,cAAc;AAClD,QAAM,qBAAqB,CAAC,CAAC,UAAU,OAAO,YAAY,SAAS;AACnE,QAAM,aAAa,qBAAqB,OAAO,YAAY,OAAO;AAClE,QAAM,eAAe,kBAAkB,cAAc,EAAE,IAAI,CAAC;AAAA,IAC1D;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,UAAM,cAAc,OAAO,eAAe,YAAY,MAAM,aAAa,eAAe,YAAY,EAAE,IAAI,eAAe,YAAY;AACrI,UAAM,gBAAgB,yBAAyB,oBAAoB,aAAa,kBAAkB,IAAI;AACtG,QAAI,aAAa,OAAO;AACtB,aAAO,cAAc,KAAK,eAAe,CAAC;AAAA,IAC5C;AACA,UAAM,gBAAgB,cAAc,KAAK,YAAY,CAAC;AACtD,WAAO,cAAc,KAAK,IAAI,mBAAiB;AAAA,MAC7C,QAAQ,eAAe,YAAY;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,CAAC;AAAA,EACL,CAAC;AACD,SAAO,MAAM,GAAG,YAAY;AAC9B;AACA,IAAM,+BAA+B;AACrC,SAAS,2BAA2B,aAAa,cAAc,mBAAmB,8BAA8B;AAC9G,SAAO,YAAY,KAAK,WAAW,WAAS;AAC1C,QAAI,aAAc,cAAa,YAAY,KAAK;AAChD,QAAI,oBAAoB,GAAG;AACzB,aAAO;AAAA,IACT;AAEA,WAAO,2BAA2B,aAAa,cAAc,mBAAmB,CAAC;AAAA,EACnF,CAAC,CAAC;AACJ;AACA,IAAM,UAAN,MAAM,iBAAgB,WAAW;AAAA,EAC/B,YAAY,QAAQ;AAClB,UAAM;AACN,QAAI,QAAQ;AACV,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,KAAK,UAAU;AACb,UAAM,aAAa,IAAI,SAAQ;AAC/B,eAAW,SAAS;AACpB,eAAW,WAAW;AACtB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,GAAG;AACtC,aAAO,KAAK,KAAK,UAAY,SAAS,qBAAqB,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,SAAQ;AAAA,MACjB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAoCH,SAAS,UAAU,cAAc;AAC/B,SAAO,OAAO,YAAU,aAAa,KAAK,yBAAuB;AAC/D,QAAI,OAAO,wBAAwB,UAAU;AAE3C,aAAO,wBAAwB,OAAO;AAAA,IACxC;AAEA,WAAO,oBAAoB,SAAS,OAAO;AAAA,EAC7C,CAAC,CAAC;AACJ;AACA,IAAM,sBAAsB,IAAI,eAAe,mCAAmC;AAClF,IAAM,wBAAwB,IAAI,eAAe,qCAAqC;AACtF,IAAM,gBAAgB,IAAI,eAAe,qCAAqC;AAC9E,IAAM,0BAA0B,IAAI,eAAe,+CAA+C;AAClG,IAAM,mBAAmB,IAAI,eAAe,wCAAwC;AACpF,IAAM,mCAAmC,IAAI,eAAe,wDAAwD;AACpH,IAAM,wBAAwB,IAAI,eAAe,uCAAuC;AAAA,EACtF,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AACD,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB,aAAa,iBAAiB;AACtD,SAAS,qBAAqB,QAAQ,UAAU;AAC9C,MAAI,OAAO,aAAa,SAAS,KAAK;AACpC,UAAM,SAAS,OAAO,aAAa;AACnC,UAAM,kBAAkB,CAAC,SAAS,MAAM;AACxC,QAAI,iBAAiB;AACnB,eAAS,YAAY,IAAI,MAAM,UAAU,cAAc,MAAM,CAAC,kCAAkC,UAAU,MAAM,CAAC,EAAE,CAAC;AAAA,IACtH;AAAA,EACF;AACF;AACA,SAAS,SAAS,QAAQ;AACxB,SAAO,OAAO,WAAW,cAAc,UAAU,OAAO,QAAQ,OAAO,OAAO,SAAS;AACzF;AACA,SAAS,cAAc;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,WAAW,OAAO,eAAe,YAAY,MAAM;AACzD,QAAM,qBAAqB,CAAC,CAAC;AAC7B,SAAO,qBAAqB,IAAI,UAAU,IAAI,OAAO,YAAY,CAAC,GAAG,WAAW,OAAO,EAAE,MAAM,IAAI,OAAO,YAAY,CAAC;AACzH;AACA,SAAS,UAAU,QAAQ;AACzB,MAAI;AACF,WAAO,KAAK,UAAU,MAAM;AAAA,EAC9B,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,IAAM,uBAAuB;AAC7B,SAAS,oBAAoB,UAAU;AACrC,SAAO,WAAW,UAAU,oBAAoB;AAClD;AACA,IAAM,kBAAkB;AACxB,SAAS,eAAe,UAAU;AAChC,SAAO,WAAW,UAAU,eAAe;AAC7C;AACA,IAAM,gBAAgB;AACtB,SAAS,gBAAgB,UAAU;AACjC,SAAO,WAAW,UAAU,aAAa;AAC3C;AACA,SAAS,WAAW,UAAU,cAAc;AAC1C,SAAO,YAAY,gBAAgB,YAAY,OAAO,SAAS,YAAY,MAAM;AACnF;AACA,IAAM,gBAAN,MAAM,uBAAsB,QAAQ;AAAA,EAClC,YAAY,cAAc,qBAAqB;AAC7C,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,WAAW,sBAAsB;AAC/B,SAAK,KAAK,oBAAoB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AACV,WAAO,KAAK,KAAK,QAAQ,CAAAA,qBAAmB,gBAAgBA,gBAAe,IAAI,qBAAqBA,gBAAe,IAAIA,gBAAe,GAAG,SAAS,aAAW;AAC3J,aAAO,QAAQ,KAAK,QAAQ,eAAe,CAAC;AAAA,IAC9C,CAAC,GAAG,SAAS,aAAW;AACtB,YAAM,UAAU,QAAQ,KAAK,WAAW,oBAAkB;AACxD,eAAO,oBAAoB,KAAK,cAAc,KAAK,mBAAmB,EAAE,cAAc;AAAA,MACxF,CAAC,GAAG,IAAI,YAAU;AAChB,6BAAqB,QAAQ,KAAK,YAAY;AAC9C,eAAO,OAAO;AAAA,MAChB,CAAC,GAAG,OAAO,kBAAgB,aAAa,SAAS,OAAO,aAAa,SAAS,IAAI,GAAG,cAAc,CAAC;AAGpG,YAAM,QAAQ,QAAQ,KAAK,KAAK,CAAC,GAAG,OAAO,eAAe,GAAG,IAAI,cAAY,SAAS,kBAAkB,CAAC,CAAC;AAC1G,aAAO,MAAM,SAAS,KAAK;AAAA,IAC7B,CAAC,CAAC;AAAA,EACJ;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAkB,SAAY,YAAY,GAAM,SAAS,qBAAqB,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,gBAAgB,gBAAgB;AACvC,MAAI,oBAAoB,cAAc,GAAG;AACvC,WAAO,eAAe,sBAAsB;AAAA,EAC9C;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,cAAc,qBAAqB;AAC9D,SAAO,oBAAkB;AACvB,UAAM,iBAAiB,aAAa,gBAAgB,cAAc,mBAAmB;AACrF,QAAI,eAAe,cAAc,GAAG;AAClC,aAAO,eAAe,iBAAiB,cAAc;AAAA,IACvD;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,IAAI,YAAY;AACd,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EACA,YAAY,eAAe,OAAO;AAChC,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AACb,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,QAAQ;AACN,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,sBAAsB,KAAK,cAAc,UAAU,EAAE,UAAU,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAY;AACrC,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAkB,SAAS,aAAa,GAAM,SAAY,KAAK,CAAC;AAAA,IACnF;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,SAAS,QAAQ,OAAO,sBAAsB,iBAAiB,oBAAoB,OAAO;AACpG,SAAK,UAAU;AACf,WAAO,MAAM;AACb,eAAWA,oBAAmB,sBAAsB;AAClD,cAAQ,WAAWA,gBAAe;AAAA,IACpC;AACA,UAAM,SAAS;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,WAAWA,kBAAiB;AAC1B,SAAK,QAAQ,WAAWA,gBAAe;AAAA,EACzC;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,SAAS,aAAa,GAAM,SAAS,aAAa,GAAM,SAAY,KAAK,GAAM,SAAS,uBAAuB,GAAM,SAAY,iBAAiB,CAAC,GAAM,SAAY,oBAAoB,CAAC,GAAM,SAAS,qBAAqB,CAAC,CAAC;AAAA,IACzQ;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,mBAAmB,uBAAuB,iBAAiB,oBAAoB;AACzF,UAAM,mBAAmB,sBAAsB,KAAK;AACpD,eAAWA,oBAAmB,kBAAkB;AAC9C,wBAAkB,WAAWA,gBAAe;AAAA,IAC9C;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,SAAS,iBAAiB,GAAM,SAAS,gCAAgC,GAAM,SAAY,iBAAiB,CAAC,GAAM,SAAY,oBAAoB,CAAC,CAAC;AAAA,IACjM;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,gCAAgC;AAAA,IACzC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,cAAc,gBAAgB;AACnC,UAAM,UAAU,eAAe,KAAK;AACpC,UAAM,iBAAiB,WAAW,OAAO;AACzC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,gBAAgB;AAAA,QAC1B,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU,CAAC;AAAA,MACb,GAAG;AAAA,QACD,SAAS;AAAA,QACT,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,MAAM,CAAC,kBAAkB,qBAAqB;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,WAAW,aAAa;AAC7B,UAAM,UAAU,YAAY,KAAK;AACjC,UAAM,iBAAiB,WAAW,OAAO;AACzC,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,gBAAgB;AAAA,QAC1B,SAAS;AAAA,QACT,UAAU,CAAC,OAAO;AAAA,MACpB,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,MACd,GAAG;AAAA,QACD,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU,CAAC;AAAA,MACb,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,MAAM,CAAC,eAAe,qBAAqB;AAAA,MAC7C,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,uBAAuB,eAAe,2BAA2B;AACxE,QAAM,UAAU,CAAC;AACjB,aAAW,gBAAgB,eAAe;AACxC,YAAQ,KAAK,GAAG,YAAY;AAAA,EAC9B;AACA,aAAW,4BAA4B,2BAA2B;AAChE,YAAQ,KAAK,GAAG,wBAAwB;AAAA,EAC1C;AACA,SAAO,QAAQ,IAAI,0BAAwB,QAAQ,oBAAoB,IAAI,OAAO,oBAAoB,IAAI,oBAAoB;AAChI;AACA,SAAS,uBAAuB;AAC9B,QAAM,SAAS,OAAO,eAAe;AAAA,IACnC,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,cAAc,OAAO,eAAe;AAAA,IACxC,MAAM;AAAA,EACR,CAAC;AAED,QAAM,aAAa,EAAE,YAAY,WAAW,KAAK,YAAY,CAAC,EAAE,WAAW;AAC3E,MAAI,cAAc,QAAQ;AACxB,UAAM,IAAI,UAAU,sGAAsG;AAAA,EAC5H;AACA,SAAO;AACT;AAOA,SAAS,IACT,iBAAiB,SAAS;AACxB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,oBAAoB,aAAa;AAAA,IAC1C,SAAS;AAAA;AAAA,IAET,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,EACf,IAAI,iCACC,kBADD;AAAA,IAEF,UAAU,gBAAgB,YAAY;AAAA,EACxC;AACA,SAAO,YAAU,MAAM,MAAM;AAC3B,UAAM,UAAU,IAAI,QAAQ;AAC5B,WAAO,MAAM,OAAO,KAAK,SAAS,CAAC,OAAO,UAAU,MAAM,MAAM;AAC9D,UAAI,YAAY;AAChB,UAAI,UAAU;AACd,UAAI,iBAAiB;AACrB,aAAO,QAAQ,OAAO,KAAK,EAAE,KAAK,YAAY,GAAG,IAAI,kBAAgB;AACnE,gBAAQ,aAAa,MAAM;AAAA,UACzB,KAAK;AACH,sBAAU;AACV,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,OAAO,MAAM,aAAa,OAAO,KAAK;AAAA,YACxC;AAAA,UACF,KAAK;AACH,wBAAY;AACZ,mBAAO,WAAW;AAAA,cAChB,MAAM;AAAA,cACN,OAAO,SAAS,gBAAgB,KAAK;AAAA,YACvC,IAAI;AAAA,UACN;AACE,cAAE;AACF,mBAAO;AAAA,QACX;AAAA,MACF,CAAC,GAAG,OAAO,OAAK,KAAK,IAAI,GAAG,cAAc,GAAG,SAAS,MAAM;AAC1D,YAAI,CAAC,aAAa,CAAC,WAAW,aAAa;AACzC,kBAAQ,KAAK,YAAY,gBAAgB,KAAK,CAAC;AAAA,QACjD;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC,CAAC,GAAG,OAAO;AAAA,EACf,CAAC;AACH;AA4BA,SAAS,kBAAkB,SAAS;AAClC,QAAM,2BAA2B,QAAQ,KAAK;AAC9C,QAAM,iBAAiB,WAAW,wBAAwB;AAC1D,SAAO,yBAAyB,CAAC,gBAAgB;AAAA,IAC/C,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU,MAAM;AACd,aAAO,mBAAmB;AAC1B,aAAO,wBAAwB;AAAA,QAC7B,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,gBAAgB,OAAO,aAAa;AAC1C,YAAM,gBAAgB,OAAO,aAAa;AAC1C,YAAM,oBAAoB,CAAC,cAAc;AACzC,UAAI,mBAAmB;AACrB,sBAAc,MAAM;AAAA,MACtB;AACA,iBAAW,wBAAwB,0BAA0B;AAC3D,cAAMA,mBAAkB,QAAQ,oBAAoB,IAAI,OAAO,oBAAoB,IAAI;AACvF,sBAAc,WAAWA,gBAAe;AAAA,MAC1C;AACA,UAAI,mBAAmB;AACrB,cAAM,QAAQ,OAAO,KAAK;AAC1B,cAAM,SAAS,gBAAgB,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACJ;AAKA,IAAMC,oBAA6B;", "names": ["effectsInstance", "concatLatestFrom"]}