/* Bank Account Selector Component Styles */
.bank-account-selector {
  width: 100%;
}

.bank-account-field {
  width: 100%;
}

/* Autocomplete Panel Styles */
.bank-account-autocomplete {
  max-height: 400px;
}

/* Bank Account Option Styles */
.bank-account-option {
  height: auto !important;
  min-height: 60px;
  padding: 10px 16px !important;
  line-height: 1.3;
  white-space: normal;
}

.bank-account-option:hover {
  background-color: #f5f5f5;
}

.account-option-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* Account Header */
.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
}

.account-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  flex: 1;
}

.account-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-active {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-inactive {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-suspended {
  background-color: #ffebee;
  color: #d32f2f;
}

.status-closed {
  background-color: #f5f5f5;
  color: #757575;
}

/* Account Details */
.account-details {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 12px;
  color: #666;
  flex-wrap: wrap;
}

.separator {
  color: #ccc;
  font-weight: bold;
}

.bank-name {
  font-weight: 500;
  color: #333;
}

.account-number {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #1976d2;
}

.account-type {
  padding: 2px 6px;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}

.account-currency {
  padding: 2px 6px;
  background-color: #f3e5f5;
  color: #7b1fa2;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}



/* Account Description */
.account-description {
  font-size: 11px;
  color: #666;
  font-style: italic;
  margin-top: 2px;
}

.description-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Account Balance */
.account-balance {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
  padding-top: 4px;
  border-top: 1px solid #e0e0e0;
}

.balance-label {
  font-size: 11px;
  color: #888;
  font-weight: 500;
}

.balance-amount {
  font-size: 12px;
  font-weight: 600;
}

.currency-ngn {
  color: #2e7d32;
}

.currency-usd {
  color: #1976d2;
}

.currency-gbp {
  color: #7b1fa2;
}

.currency-eur {
  color: #f57c00;
}

/* No Results */
.no-results {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  color: #999;
  font-style: italic;
}

.no-results mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Loading Spinner */
.mat-mdc-form-field-suffix .mat-spinner {
  margin-right: 8px;
}

/* Clear Button */
.mat-mdc-form-field-suffix .mat-mdc-icon-button {
  width: 24px;
  height: 24px;
  margin-right: 4px;
}

.mat-mdc-form-field-suffix .mat-mdc-icon-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .bank-account-option {
    min-height: 55px;
    padding: 8px 12px !important;
  }

  .account-option-content {
    gap: 2px;
  }

  .account-name {
    font-size: 13px;
  }

  .account-details {
    gap: 6px;
    font-size: 11px;
  }

  .account-type {
    font-size: 9px;
    padding: 1px 4px;
  }
}

/* Focus and Accessibility */
.bank-account-option:focus {
  background-color: #e3f2fd;
  outline: 2px solid #1976d2;
  outline-offset: -2px;
}

.bank-account-option[aria-selected="true"] {
  background-color: #e3f2fd;
}

/* Error State */
.mat-mdc-form-field.mat-form-field-invalid .bank-account-field {
  border-color: #f44336;
}

/* Disabled State */
.bank-account-selector .mat-mdc-form-field.mat-form-field-disabled {
  opacity: 0.6;
}

.bank-account-selector .mat-mdc-form-field.mat-form-field-disabled .mat-mdc-text-field-wrapper {
  background-color: #f5f5f5;
}
