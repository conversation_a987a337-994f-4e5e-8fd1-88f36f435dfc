{"version": 3, "sources": ["../../../../../node_modules/@angular/material/fesm2022/paginator.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Optional, SkipSelf, InjectionToken, numberAttribute, EventEmitter, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { MatIconButton, MatButtonModule } from '@angular/material/button';\nimport { MatSelect, MatSelectModule } from '@angular/material/select';\nimport { MatTooltip, MatTooltipModule } from '@angular/material/tooltip';\nimport { MatOption } from '@angular/material/core';\nimport { MatFormField } from '@angular/material/form-field';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\nfunction MatPaginator_Conditional_2_Conditional_3_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const pageSizeOption_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", pageSizeOption_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", pageSizeOption_r3, \" \");\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-form-field\", 13)(1, \"mat-select\", 15);\n    i0.ɵɵlistener(\"selectionChange\", function MatPaginator_Conditional_2_Conditional_3_Template_mat_select_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1._changePageSize($event.value));\n    });\n    i0.ɵɵrepeaterCreate(2, MatPaginator_Conditional_2_Conditional_3_For_3_Template, 2, 2, \"mat-option\", 16, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"appearance\", ctx_r1._formFieldAppearance)(\"color\", ctx_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.pageSize)(\"disabled\", ctx_r1.disabled)(\"aria-labelledby\", ctx_r1._pageSizeLabelId)(\"panelClass\", ctx_r1.selectConfig.panelClass || \"\")(\"disableOptionCentering\", ctx_r1.selectConfig.disableOptionCentering);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r1._displayedPageSizeOptions);\n  }\n}\nfunction MatPaginator_Conditional_2_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pageSize);\n  }\n}\nfunction MatPaginator_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MatPaginator_Conditional_2_Conditional_3_Template, 4, 7, \"mat-form-field\", 13)(4, MatPaginator_Conditional_2_Conditional_4_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"id\", ctx_r1._pageSizeLabelId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1._intl.itemsPerPageLabel, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(3, ctx_r1._displayedPageSizeOptions.length > 1 ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, ctx_r1._displayedPageSizeOptions.length <= 1 ? 4 : -1);\n  }\n}\nfunction MatPaginator_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.firstPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.firstPageLabel)(\"matTooltipDisabled\", ctx_r1._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r1._previousButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.firstPageLabel);\n  }\n}\nfunction MatPaginator_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function MatPaginator_Conditional_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.lastPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 7);\n    i0.ɵɵelement(2, \"path\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1._intl.lastPageLabel)(\"matTooltipDisabled\", ctx_r1._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx_r1._nextButtonsDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1._intl.lastPageLabel);\n  }\n}\nclass MatPaginatorIntl {\n  constructor() {\n    /**\n     * Stream to emit from when labels are changed. Use this to notify components when the labels have\n     * changed after initialization.\n     */\n    this.changes = new Subject();\n    /** A label for the page size selector. */\n    this.itemsPerPageLabel = 'Items per page:';\n    /** A label for the button that increments the current page. */\n    this.nextPageLabel = 'Next page';\n    /** A label for the button that decrements the current page. */\n    this.previousPageLabel = 'Previous page';\n    /** A label for the button that moves to the first page. */\n    this.firstPageLabel = 'First page';\n    /** A label for the button that moves to the last page. */\n    this.lastPageLabel = 'Last page';\n    /** A label for the range of items within the current page and the length of the whole list. */\n    this.getRangeLabel = (page, pageSize, length) => {\n      if (length == 0 || pageSize == 0) {\n        return `0 of ${length}`;\n      }\n      length = Math.max(length, 0);\n      const startIndex = page * pageSize;\n      // If the start index exceeds the list length, do not try and fix the end index to the end.\n      const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n      return `${startIndex + 1} – ${endIndex} of ${length}`;\n    };\n  }\n  static {\n    this.ɵfac = function MatPaginatorIntl_Factory(t) {\n      return new (t || MatPaginatorIntl)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatPaginatorIntl,\n      factory: MatPaginatorIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\nfunction MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n/** @docs-private */\nconst MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY\n};\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nclass PageEvent {}\n/** Injection token that can be used to provide the default options for the paginator module. */\nconst MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken('MAT_PAGINATOR_DEFAULT_OPTIONS');\nlet nextUniqueId = 0;\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\nclass MatPaginator {\n  /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n  get pageIndex() {\n    return this._pageIndex;\n  }\n  set pageIndex(value) {\n    this._pageIndex = Math.max(value || 0, 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** The length of the total number of items that are being paginated. Defaulted to 0. */\n  get length() {\n    return this._length;\n  }\n  set length(value) {\n    this._length = value || 0;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Number of items to display on a page. By default set to 50. */\n  get pageSize() {\n    return this._pageSize;\n  }\n  set pageSize(value) {\n    this._pageSize = Math.max(value || 0, 0);\n    this._updateDisplayedPageSizeOptions();\n  }\n  /** The set of provided page size options to display to the user. */\n  get pageSizeOptions() {\n    return this._pageSizeOptions;\n  }\n  set pageSizeOptions(value) {\n    this._pageSizeOptions = (value || []).map(p => numberAttribute(p, 0));\n    this._updateDisplayedPageSizeOptions();\n  }\n  constructor(_intl, _changeDetectorRef, defaults) {\n    this._intl = _intl;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** ID for the DOM node containing the paginator's items per page label. */\n    this._pageSizeLabelId = `mat-paginator-page-size-label-${nextUniqueId++}`;\n    this._isInitialized = false;\n    this._initializedStream = new ReplaySubject(1);\n    this._pageIndex = 0;\n    this._length = 0;\n    this._pageSizeOptions = [];\n    /** Whether to hide the page size selection UI from the user. */\n    this.hidePageSize = false;\n    /** Whether to show the first/last buttons UI to the user. */\n    this.showFirstLastButtons = false;\n    /** Used to configure the underlying `MatSelect` inside the paginator. */\n    this.selectConfig = {};\n    /** Whether the paginator is disabled. */\n    this.disabled = false;\n    /** Event emitted when the paginator changes the page size or page index. */\n    this.page = new EventEmitter();\n    /** Emits when the paginator is initialized. */\n    this.initialized = this._initializedStream;\n    this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n    if (defaults) {\n      const {\n        pageSize,\n        pageSizeOptions,\n        hidePageSize,\n        showFirstLastButtons\n      } = defaults;\n      if (pageSize != null) {\n        this._pageSize = pageSize;\n      }\n      if (pageSizeOptions != null) {\n        this._pageSizeOptions = pageSizeOptions;\n      }\n      if (hidePageSize != null) {\n        this.hidePageSize = hidePageSize;\n      }\n      if (showFirstLastButtons != null) {\n        this.showFirstLastButtons = showFirstLastButtons;\n      }\n    }\n    this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._updateDisplayedPageSizeOptions();\n    this._initializedStream.next();\n  }\n  ngOnDestroy() {\n    this._initializedStream.complete();\n    this._intlChanges.unsubscribe();\n  }\n  /** Advances to the next page if it exists. */\n  nextPage() {\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex + 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move back to the previous page if it exists. */\n  previousPage() {\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.pageIndex - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the first page if not already there. */\n  firstPage() {\n    // hasPreviousPage being false implies at the start\n    if (!this.hasPreviousPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = 0;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Move to the last page if not already there. */\n  lastPage() {\n    // hasNextPage being false implies at the end\n    if (!this.hasNextPage()) {\n      return;\n    }\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = this.getNumberOfPages() - 1;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Whether there is a previous page. */\n  hasPreviousPage() {\n    return this.pageIndex >= 1 && this.pageSize != 0;\n  }\n  /** Whether there is a next page. */\n  hasNextPage() {\n    const maxPageIndex = this.getNumberOfPages() - 1;\n    return this.pageIndex < maxPageIndex && this.pageSize != 0;\n  }\n  /** Calculate the number of pages */\n  getNumberOfPages() {\n    if (!this.pageSize) {\n      return 0;\n    }\n    return Math.ceil(this.length / this.pageSize);\n  }\n  /**\n   * Changes the page size so that the first item displayed on the page will still be\n   * displayed using the new page size.\n   *\n   * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n   * switching so that the page size is 5 will set the third page as the current page so\n   * that the 10th item will still be displayed.\n   */\n  _changePageSize(pageSize) {\n    // Current page needs to be updated to reflect the new page size. Navigate to the page\n    // containing the previous page's first item.\n    const startIndex = this.pageIndex * this.pageSize;\n    const previousPageIndex = this.pageIndex;\n    this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n    this.pageSize = pageSize;\n    this._emitPageEvent(previousPageIndex);\n  }\n  /** Checks whether the buttons for going forwards should be disabled. */\n  _nextButtonsDisabled() {\n    return this.disabled || !this.hasNextPage();\n  }\n  /** Checks whether the buttons for going backwards should be disabled. */\n  _previousButtonsDisabled() {\n    return this.disabled || !this.hasPreviousPage();\n  }\n  /**\n   * Updates the list of page size options to display to the user. Includes making sure that\n   * the page size is an option and that the list is sorted.\n   */\n  _updateDisplayedPageSizeOptions() {\n    if (!this._isInitialized) {\n      return;\n    }\n    // If no page size is provided, use the first page size option or the default page size.\n    if (!this.pageSize) {\n      this._pageSize = this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n    }\n    this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n    if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n      this._displayedPageSizeOptions.push(this.pageSize);\n    }\n    // Sort the numbers using a number-specific sort function.\n    this._displayedPageSizeOptions.sort((a, b) => a - b);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n  _emitPageEvent(previousPageIndex) {\n    this.page.emit({\n      previousPageIndex,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      length: this.length\n    });\n  }\n  static {\n    this.ɵfac = function MatPaginator_Factory(t) {\n      return new (t || MatPaginator)(i0.ɵɵdirectiveInject(MatPaginatorIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_PAGINATOR_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatPaginator,\n      selectors: [[\"mat-paginator\"]],\n      hostAttrs: [\"role\", \"group\", 1, \"mat-mdc-paginator\"],\n      inputs: {\n        color: \"color\",\n        pageIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"pageIndex\", \"pageIndex\", numberAttribute],\n        length: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"length\", \"length\", numberAttribute],\n        pageSize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"pageSize\", \"pageSize\", numberAttribute],\n        pageSizeOptions: \"pageSizeOptions\",\n        hidePageSize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hidePageSize\", \"hidePageSize\", booleanAttribute],\n        showFirstLastButtons: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showFirstLastButtons\", \"showFirstLastButtons\", booleanAttribute],\n        selectConfig: \"selectConfig\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        page: \"page\"\n      },\n      exportAs: [\"matPaginator\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 14,\n      consts: [[1, \"mat-mdc-paginator-outer-container\"], [1, \"mat-mdc-paginator-container\"], [1, \"mat-mdc-paginator-page-size\"], [1, \"mat-mdc-paginator-range-actions\"], [\"aria-live\", \"polite\", 1, \"mat-mdc-paginator-range-label\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-previous\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"viewBox\", \"0 0 24 24\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-paginator-icon\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-next\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [1, \"mat-mdc-paginator-page-size-label\"], [1, \"mat-mdc-paginator-page-size-select\", 3, \"appearance\", \"color\"], [1, \"mat-mdc-paginator-page-size-value\"], [\"hideSingleSelectionIndicator\", \"\", 3, \"selectionChange\", \"value\", \"disabled\", \"aria-labelledby\", \"panelClass\", \"disableOptionCentering\"], [3, \"value\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-first\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"d\", \"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"], [\"mat-icon-button\", \"\", \"type\", \"button\", 1, \"mat-mdc-paginator-navigation-last\", 3, \"click\", \"matTooltip\", \"matTooltipDisabled\", \"matTooltipPosition\", \"disabled\"], [\"d\", \"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"]],\n      template: function MatPaginator_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, MatPaginator_Conditional_2_Template, 5, 4, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, MatPaginator_Conditional_6_Template, 3, 5, \"button\", 5);\n          i0.ɵɵelementStart(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_7_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 7);\n          i0.ɵɵelement(9, \"path\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(10, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function MatPaginator_Template_button_click_10_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(11, \"svg\", 7);\n          i0.ɵɵelement(12, \"path\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, MatPaginator_Conditional_13_Template, 3, 5, \"button\", 11);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, !ctx.hidePageSize ? 2 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx._intl.getRangeLabel(ctx.pageIndex, ctx.pageSize, ctx.length), \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(6, ctx.showFirstLastButtons ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.previousPageLabel)(\"matTooltipDisabled\", ctx._previousButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._previousButtonsDisabled());\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.previousPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matTooltip\", ctx._intl.nextPageLabel)(\"matTooltipDisabled\", ctx._nextButtonsDisabled())(\"matTooltipPosition\", \"above\")(\"disabled\", ctx._nextButtonsDisabled());\n          i0.ɵɵattribute(\"aria-label\", ctx._intl.nextPageLabel);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(13, ctx.showFirstLastButtons ? 13 : -1);\n        }\n      },\n      dependencies: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking);--mat-form-field-container-height:var(--mat-paginator-form-field-container-height);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginator, [{\n    type: Component,\n    args: [{\n      selector: 'mat-paginator',\n      exportAs: 'matPaginator',\n      host: {\n        'class': 'mat-mdc-paginator',\n        'role': 'group'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n      template: \"<div class=\\\"mat-mdc-paginator-outer-container\\\">\\n  <div class=\\\"mat-mdc-paginator-container\\\">\\n    @if (!hidePageSize) {\\n      <div class=\\\"mat-mdc-paginator-page-size\\\">\\n        <div class=\\\"mat-mdc-paginator-page-size-label\\\" [attr.id]=\\\"_pageSizeLabelId\\\">\\n          {{_intl.itemsPerPageLabel}}\\n        </div>\\n\\n        @if (_displayedPageSizeOptions.length > 1) {\\n          <mat-form-field\\n            [appearance]=\\\"_formFieldAppearance!\\\"\\n            [color]=\\\"color\\\"\\n            class=\\\"mat-mdc-paginator-page-size-select\\\">\\n            <mat-select\\n              [value]=\\\"pageSize\\\"\\n              [disabled]=\\\"disabled\\\"\\n              [aria-labelledby]=\\\"_pageSizeLabelId\\\"\\n              [panelClass]=\\\"selectConfig.panelClass || ''\\\"\\n              [disableOptionCentering]=\\\"selectConfig.disableOptionCentering\\\"\\n              (selectionChange)=\\\"_changePageSize($event.value)\\\"\\n              hideSingleSelectionIndicator>\\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\\n                <mat-option [value]=\\\"pageSizeOption\\\">\\n                  {{pageSizeOption}}\\n                </mat-option>\\n              }\\n            </mat-select>\\n          </mat-form-field>\\n        }\\n\\n        @if (_displayedPageSizeOptions.length <= 1) {\\n          <div class=\\\"mat-mdc-paginator-page-size-value\\\">{{pageSize}}</div>\\n        }\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-paginator-range-actions\\\">\\n      <div class=\\\"mat-mdc-paginator-range-label\\\" aria-live=\\\"polite\\\">\\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\\n      </div>\\n\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-first\\\"\\n                (click)=\\\"firstPage()\\\"\\n                [attr.aria-label]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltip]=\\\"_intl.firstPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_previousButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-previous\\\"\\n              (click)=\\\"previousPage()\\\"\\n              [attr.aria-label]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltip]=\\\"_intl.previousPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_previousButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_previousButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\\\"/>\\n        </svg>\\n      </button>\\n      <button mat-icon-button type=\\\"button\\\"\\n              class=\\\"mat-mdc-paginator-navigation-next\\\"\\n              (click)=\\\"nextPage()\\\"\\n              [attr.aria-label]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltip]=\\\"_intl.nextPageLabel\\\"\\n              [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n              [matTooltipPosition]=\\\"'above'\\\"\\n              [disabled]=\\\"_nextButtonsDisabled()\\\">\\n        <svg class=\\\"mat-mdc-paginator-icon\\\"\\n             viewBox=\\\"0 0 24 24\\\"\\n             focusable=\\\"false\\\"\\n             aria-hidden=\\\"true\\\">\\n          <path d=\\\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\\\"/>\\n        </svg>\\n      </button>\\n      @if (showFirstLastButtons) {\\n        <button mat-icon-button type=\\\"button\\\"\\n                class=\\\"mat-mdc-paginator-navigation-last\\\"\\n                (click)=\\\"lastPage()\\\"\\n                [attr.aria-label]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltip]=\\\"_intl.lastPageLabel\\\"\\n                [matTooltipDisabled]=\\\"_nextButtonsDisabled()\\\"\\n                [matTooltipPosition]=\\\"'above'\\\"\\n                [disabled]=\\\"_nextButtonsDisabled()\\\">\\n          <svg class=\\\"mat-mdc-paginator-icon\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              focusable=\\\"false\\\"\\n              aria-hidden=\\\"true\\\">\\n            <path d=\\\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\\\"/>\\n          </svg>\\n        </button>\\n      }\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-paginator{display:block;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-paginator-container-text-color);background-color:var(--mat-paginator-container-background-color);font-family:var(--mat-paginator-container-text-font);line-height:var(--mat-paginator-container-text-line-height);font-size:var(--mat-paginator-container-text-size);font-weight:var(--mat-paginator-container-text-weight);letter-spacing:var(--mat-paginator-container-text-tracking);--mat-form-field-container-height:var(--mat-paginator-form-field-container-height);--mat-form-field-container-vertical-padding:var(--mat-paginator-form-field-container-vertical-padding)}.mat-mdc-paginator .mat-mdc-select-value{font-size:var(--mat-paginator-select-trigger-text-size)}.mat-mdc-paginator .mat-mdc-form-field-subscript-wrapper{display:none}.mat-mdc-paginator .mat-mdc-select{line-height:1.5}.mat-mdc-paginator-outer-container{display:flex}.mat-mdc-paginator-container{display:flex;align-items:center;justify-content:flex-end;padding:0 8px;flex-wrap:wrap;width:100%;min-height:var(--mat-paginator-container-size)}.mat-mdc-paginator-page-size{display:flex;align-items:baseline;margin-right:8px}[dir=rtl] .mat-mdc-paginator-page-size{margin-right:0;margin-left:8px}.mat-mdc-paginator-page-size-label{margin:0 4px}.mat-mdc-paginator-page-size-select{margin:0 4px;width:84px}.mat-mdc-paginator-range-label{margin:0 32px 0 24px}.mat-mdc-paginator-range-actions{display:flex;align-items:center}.mat-mdc-paginator-icon{display:inline-block;width:28px;fill:var(--mat-paginator-enabled-icon-color)}.mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon{fill:var(--mat-paginator-disabled-icon-color)}[dir=rtl] .mat-mdc-paginator-icon{transform:rotate(180deg)}.cdk-high-contrast-active .mat-mdc-icon-button[disabled] .mat-mdc-paginator-icon,.cdk-high-contrast-active .mat-mdc-paginator-icon{fill:currentColor;fill:CanvasText}.cdk-high-contrast-active .mat-mdc-paginator-range-actions .mat-mdc-icon-button{outline:solid 1px}\"]\n    }]\n  }], () => [{\n    type: MatPaginatorIntl\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_PAGINATOR_DEFAULT_OPTIONS]\n    }]\n  }], {\n    color: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    length: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    pageSize: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    hidePageSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showFirstLastButtons: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectConfig: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    page: [{\n      type: Output\n    }]\n  });\n})();\nclass MatPaginatorModule {\n  static {\n    this.ɵfac = function MatPaginatorModule_Factory(t) {\n      return new (t || MatPaginatorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatPaginatorModule,\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n      exports: [MatPaginator]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_PAGINATOR_INTL_PROVIDER],\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPaginatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n      exports: [MatPaginator],\n      providers: [MAT_PAGINATOR_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PAGINATOR_DEFAULT_OPTIONS, MAT_PAGINATOR_INTL_PROVIDER, MAT_PAGINATOR_INTL_PROVIDER_FACTORY, MatPaginator, MatPaginatorIntl, MatPaginatorModule, PageEvent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,cAAc,EAAE;AACrC,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,oBAAoB,IAAI;AAC9B,IAAG,WAAW,SAAS,iBAAiB;AACxC,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,mBAAmB,GAAG;AAAA,EACnD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,kBAAkB,EAAE,EAAE,GAAG,cAAc,EAAE;AAC9D,IAAG,WAAW,mBAAmB,SAAS,wFAAwF,QAAQ;AACxI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,gBAAgB,OAAO,KAAK,CAAC;AAAA,IAC5D,CAAC;AACD,IAAG,iBAAiB,GAAG,yDAAyD,GAAG,GAAG,cAAc,IAAO,yBAAyB;AACpI,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,cAAc,OAAO,oBAAoB,EAAE,SAAS,OAAO,KAAK;AAC9E,IAAG,UAAU;AACb,IAAG,WAAW,SAAS,OAAO,QAAQ,EAAE,YAAY,OAAO,QAAQ,EAAE,mBAAmB,OAAO,gBAAgB,EAAE,cAAc,OAAO,aAAa,cAAc,EAAE,EAAE,0BAA0B,OAAO,aAAa,sBAAsB;AACzO,IAAG,UAAU;AACb,IAAG,WAAW,OAAO,yBAAyB;AAAA,EAChD;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,QAAQ;AAAA,EACtC;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,EAAE;AAC3C,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,kBAAkB,EAAE,EAAE,GAAG,mDAAmD,GAAG,GAAG,OAAO,EAAE;AACrK,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,YAAY,MAAM,OAAO,gBAAgB;AAC5C,IAAG,UAAU;AACb,IAAG,mBAAmB,KAAK,OAAO,MAAM,mBAAmB,GAAG;AAC9D,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,0BAA0B,SAAS,IAAI,IAAI,EAAE;AACxE,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,0BAA0B,UAAU,IAAI,IAAI,EAAE;AAAA,EAC3E;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,8DAA8D;AAC5F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,MAAM,cAAc,EAAE,sBAAsB,OAAO,yBAAyB,CAAC,EAAE,sBAAsB,OAAO,EAAE,YAAY,OAAO,yBAAyB,CAAC;AAC9L,IAAG,YAAY,cAAc,OAAO,MAAM,cAAc;AAAA,EAC1D;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,+DAA+D;AAC7F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,SAAS,CAAC;AAAA,IACzC,CAAC;AACD,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,cAAc,OAAO,MAAM,aAAa,EAAE,sBAAsB,OAAO,qBAAqB,CAAC,EAAE,sBAAsB,OAAO,EAAE,YAAY,OAAO,qBAAqB,CAAC;AACrL,IAAG,YAAY,cAAc,OAAO,MAAM,aAAa;AAAA,EACzD;AACF;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AAKZ,SAAK,UAAU,IAAI,QAAQ;AAE3B,SAAK,oBAAoB;AAEzB,SAAK,gBAAgB;AAErB,SAAK,oBAAoB;AAEzB,SAAK,iBAAiB;AAEtB,SAAK,gBAAgB;AAErB,SAAK,gBAAgB,CAAC,MAAM,UAAU,WAAW;AAC/C,UAAI,UAAU,KAAK,YAAY,GAAG;AAChC,eAAO,QAAQ,MAAM;AAAA,MACvB;AACA,eAAS,KAAK,IAAI,QAAQ,CAAC;AAC3B,YAAM,aAAa,OAAO;AAE1B,YAAM,WAAW,aAAa,SAAS,KAAK,IAAI,aAAa,UAAU,MAAM,IAAI,aAAa;AAC9F,aAAO,GAAG,aAAa,CAAC,MAAM,QAAQ,OAAO,MAAM;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,SAAS,oCAAoC,YAAY;AACvD,SAAO,cAAc,IAAI,iBAAiB;AAC5C;AAEA,IAAM,8BAA8B;AAAA;AAAA,EAElC,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,gBAAgB,CAAC;AAAA,EACzD,YAAY;AACd;AAGA,IAAM,oBAAoB;AAK1B,IAAM,YAAN,MAAgB;AAAC;AAEjB,IAAM,gCAAgC,IAAI,eAAe,+BAA+B;AACxF,IAAI,eAAe;AAMnB,IAAM,eAAN,MAAM,cAAa;AAAA;AAAA,EAEjB,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,aAAa,KAAK,IAAI,SAAS,GAAG,CAAC;AACxC,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,SAAK,UAAU,SAAS;AACxB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,KAAK,IAAI,SAAS,GAAG,CAAC;AACvC,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB,OAAO;AACzB,SAAK,oBAAoB,SAAS,CAAC,GAAG,IAAI,OAAK,gBAAgB,GAAG,CAAC,CAAC;AACpE,SAAK,gCAAgC;AAAA,EACvC;AAAA,EACA,YAAY,OAAO,oBAAoB,UAAU;AAC/C,SAAK,QAAQ;AACb,SAAK,qBAAqB;AAE1B,SAAK,mBAAmB,iCAAiC,cAAc;AACvE,SAAK,iBAAiB;AACtB,SAAK,qBAAqB,IAAI,cAAc,CAAC;AAC7C,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,mBAAmB,CAAC;AAEzB,SAAK,eAAe;AAEpB,SAAK,uBAAuB;AAE5B,SAAK,eAAe,CAAC;AAErB,SAAK,WAAW;AAEhB,SAAK,OAAO,IAAI,aAAa;AAE7B,SAAK,cAAc,KAAK;AACxB,SAAK,eAAe,MAAM,QAAQ,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AACxF,QAAI,UAAU;AACZ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,YAAY,MAAM;AACpB,aAAK,YAAY;AAAA,MACnB;AACA,UAAI,mBAAmB,MAAM;AAC3B,aAAK,mBAAmB;AAAA,MAC1B;AACA,UAAI,gBAAgB,MAAM;AACxB,aAAK,eAAe;AAAA,MACtB;AACA,UAAI,wBAAwB,MAAM;AAChC,aAAK,uBAAuB;AAAA,MAC9B;AAAA,IACF;AACA,SAAK,uBAAuB,UAAU,uBAAuB;AAAA,EAC/D;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB;AACtB,SAAK,gCAAgC;AACrC,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB,SAAS;AACjC,SAAK,aAAa,YAAY;AAAA,EAChC;AAAA;AAAA,EAEA,WAAW;AACT,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY,KAAK,YAAY;AAClC,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,eAAe;AACb,QAAI,CAAC,KAAK,gBAAgB,GAAG;AAC3B;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY,KAAK,YAAY;AAClC,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,YAAY;AAEV,QAAI,CAAC,KAAK,gBAAgB,GAAG;AAC3B;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY;AACjB,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,WAAW;AAET,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY,KAAK,iBAAiB,IAAI;AAC3C,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,aAAa,KAAK,KAAK,YAAY;AAAA,EACjD;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,eAAe,KAAK,iBAAiB,IAAI;AAC/C,WAAO,KAAK,YAAY,gBAAgB,KAAK,YAAY;AAAA,EAC3D;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,KAAK,KAAK,SAAS,KAAK,QAAQ;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,UAAU;AAGxB,UAAM,aAAa,KAAK,YAAY,KAAK;AACzC,UAAM,oBAAoB,KAAK;AAC/B,SAAK,YAAY,KAAK,MAAM,aAAa,QAAQ,KAAK;AACtD,SAAK,WAAW;AAChB,SAAK,eAAe,iBAAiB;AAAA,EACvC;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK,YAAY,CAAC,KAAK,YAAY;AAAA,EAC5C;AAAA;AAAA,EAEA,2BAA2B;AACzB,WAAO,KAAK,YAAY,CAAC,KAAK,gBAAgB;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kCAAkC;AAChC,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,YAAY,KAAK,gBAAgB,UAAU,IAAI,KAAK,gBAAgB,CAAC,IAAI;AAAA,IAChF;AACA,SAAK,4BAA4B,KAAK,gBAAgB,MAAM;AAC5D,QAAI,KAAK,0BAA0B,QAAQ,KAAK,QAAQ,MAAM,IAAI;AAChE,WAAK,0BAA0B,KAAK,KAAK,QAAQ;AAAA,IACnD;AAEA,SAAK,0BAA0B,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACnD,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA;AAAA,EAEA,eAAe,mBAAmB;AAChC,SAAK,KAAK,KAAK;AAAA,MACb;AAAA,MACA,WAAW,KAAK;AAAA,MAChB,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAiB,kBAAkB,gBAAgB,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,+BAA+B,CAAC,CAAC;AAAA,IAC3K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,QAAQ,SAAS,GAAG,mBAAmB;AAAA,MACnD,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,WAAW,CAAI,WAAa,4BAA4B,aAAa,aAAa,eAAe;AAAA,QACjG,QAAQ,CAAI,WAAa,4BAA4B,UAAU,UAAU,eAAe;AAAA,QACxF,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,eAAe;AAAA,QAC9F,iBAAiB;AAAA,QACjB,cAAc,CAAI,WAAa,4BAA4B,gBAAgB,gBAAgB,gBAAgB;AAAA,QAC3G,sBAAsB,CAAI,WAAa,4BAA4B,wBAAwB,wBAAwB,gBAAgB;AAAA,QACnI,cAAc;AAAA,QACd,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,MACjG;AAAA,MACA,SAAS;AAAA,QACP,MAAM;AAAA,MACR;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,mBAAmB;AAAA,MAC9D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,mCAAmC,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,iCAAiC,GAAG,CAAC,aAAa,UAAU,GAAG,+BAA+B,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,sCAAsC,GAAG,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,yCAAyC,GAAG,SAAS,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,WAAW,aAAa,aAAa,SAAS,eAAe,QAAQ,GAAG,wBAAwB,GAAG,CAAC,KAAK,+CAA+C,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,qCAAqC,GAAG,SAAS,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,KAAK,gDAAgD,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,qCAAqC,GAAG,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,GAAG,mCAAmC,GAAG,CAAC,GAAG,sCAAsC,GAAG,cAAc,OAAO,GAAG,CAAC,GAAG,mCAAmC,GAAG,CAAC,gCAAgC,IAAI,GAAG,mBAAmB,SAAS,YAAY,mBAAmB,cAAc,wBAAwB,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,sCAAsC,GAAG,SAAS,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,KAAK,4DAA4D,GAAG,CAAC,mBAAmB,IAAI,QAAQ,UAAU,GAAG,qCAAqC,GAAG,SAAS,cAAc,sBAAsB,sBAAsB,UAAU,GAAG,CAAC,KAAK,4DAA4D,CAAC;AAAA,MACl0D,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,OAAO,CAAC;AACpE,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,OAAO,CAAC;AACX,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,UAAU,CAAC;AACvE,UAAG,eAAe,GAAG,UAAU,CAAC;AAChC,UAAG,WAAW,SAAS,SAAS,gDAAgD;AAC9E,mBAAO,IAAI,aAAa;AAAA,UAC1B,CAAC;AACD,UAAG,eAAe;AAClB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,UAAG,aAAa,EAAE;AAClB,UAAG,gBAAgB;AACnB,UAAG,eAAe,IAAI,UAAU,CAAC;AACjC,UAAG,WAAW,SAAS,SAAS,iDAAiD;AAC/E,mBAAO,IAAI,SAAS;AAAA,UACtB,CAAC;AACD,UAAG,eAAe;AAClB,UAAG,eAAe,IAAI,OAAO,CAAC;AAC9B,UAAG,UAAU,IAAI,QAAQ,EAAE;AAC3B,UAAG,aAAa,EAAE;AAClB,UAAG,WAAW,IAAI,sCAAsC,GAAG,GAAG,UAAU,EAAE;AAC1E,UAAG,aAAa,EAAE,EAAE;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,CAAC,IAAI,eAAe,IAAI,EAAE;AAC9C,UAAG,UAAU,CAAC;AACd,UAAG,mBAAmB,KAAK,IAAI,MAAM,cAAc,IAAI,WAAW,IAAI,UAAU,IAAI,MAAM,GAAG,GAAG;AAChG,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,uBAAuB,IAAI,EAAE;AACrD,UAAG,UAAU;AACb,UAAG,WAAW,cAAc,IAAI,MAAM,iBAAiB,EAAE,sBAAsB,IAAI,yBAAyB,CAAC,EAAE,sBAAsB,OAAO,EAAE,YAAY,IAAI,yBAAyB,CAAC;AACxL,UAAG,YAAY,cAAc,IAAI,MAAM,iBAAiB;AACxD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,cAAc,IAAI,MAAM,aAAa,EAAE,sBAAsB,IAAI,qBAAqB,CAAC,EAAE,sBAAsB,OAAO,EAAE,YAAY,IAAI,qBAAqB,CAAC;AAC5K,UAAG,YAAY,cAAc,IAAI,MAAM,aAAa;AACpD,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,IAAI,uBAAuB,KAAK,EAAE;AAAA,QACzD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAc,WAAW,WAAW,eAAe,UAAU;AAAA,MAC5E,QAAQ,CAAC,o+DAAo+D;AAAA,MAC7+D,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,WAAW,WAAW,eAAe,UAAU;AAAA,MACvE,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,o+DAAo+D;AAAA,IAC/+D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,iBAAiB,kBAAkB,YAAY;AAAA,MAC1E,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,2BAA2B;AAAA,MACvC,SAAS,CAAC,iBAAiB,iBAAiB,kBAAkB,YAAY;AAAA,IAC5E,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,iBAAiB,kBAAkB,YAAY;AAAA,MAC1E,SAAS,CAAC,YAAY;AAAA,MACtB,WAAW,CAAC,2BAA2B;AAAA,IACzC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}