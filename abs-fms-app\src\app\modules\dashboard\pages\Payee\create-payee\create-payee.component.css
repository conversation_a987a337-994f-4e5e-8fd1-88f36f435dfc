/* Container */
.container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex-wrap: wrap;
  gap: 16px;
}

.title-section h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.title-section p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.action-buttons button {
  min-width: 160px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  text-transform: none;
  border-radius: 6px;
}

.action-buttons button mat-icon {
  margin-right: 8px;
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Content Section */
.content-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-top: 20px;
}

/* Search Section */
.search-section {
  margin-bottom: 24px;
  display: flex;
  justify-content: flex-start;
}

.search-field {
  width: 33.333%;
  max-width: 400px;
  min-width: 250px;
}

/* Table Container */
.table-container {
  width: 100%;
  overflow-x: auto;
}

.payees-table {
  width: 100%;
  background: white;
}

.payees-table .mat-mdc-header-cell {
  font-weight: 600;
  color: #333;
  background-color: #f8f9fa;
}

.payees-table .mat-mdc-cell {
  padding: 12px 8px;
  border-bottom: 1px solid #e0e0e0;
}

.payees-table .mat-mdc-row:hover {
  background-color: #f5f5f5;
}

/* Status Badges */
.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-inactive {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-suspended {
  background-color: #ffebee;
  color: #d32f2f;
}

/* Table Action Buttons */
.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  min-width: 180px;
}

.action-buttons .action-btn {
  width: 36px;
  height: 28px;
  min-width: 36px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 11px;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  flex-shrink: 0;
}

.action-buttons .action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.action-buttons .action-btn mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin: 0;
}

/* Specific button colors for table actions */
.action-buttons .action-btn[color="primary"] {
  background-color: #1976d2;
  color: white;
  border: none;
}

.action-buttons .action-btn[color="primary"]:hover {
  background-color: #1565c0;
}

.action-buttons .action-btn[color="accent"] {
  background-color: #388e3c;
  color: white;
  border: none;
}

.action-buttons .action-btn[color="accent"]:hover {
  background-color: #2e7d32;
}

.action-buttons .action-btn[color="warn"] {
  background-color: #d32f2f;
  color: white;
  border: none;
}

.action-buttons .action-btn[color="warn"]:hover {
  background-color: #c62828;
}

/* No Data Message */
.no-data-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-data-message mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #ccc;
  margin-bottom: 16px;
}

.no-data-message p {
  margin: 8px 0;
}

.no-data-subtitle {
  font-size: 0.9rem;
  color: #999;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

/* Form Styles */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.full-width {
  width: 100%;
}

.half-width {
  width: calc(50% - 8px);
}

/* View Details Modal */
.view-details-modal {
  max-width: 700px;
  width: 95%;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 8px 0;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  color: #333;
  font-size: 1rem;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #e0e0e0;
}

.account-highlight {
  background-color: #e3f2fd;
  border-left-color: #2196f3;
  font-weight: 600;
  color: #1976d2;
}

/* Edit Form Styles */
.edit-form {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16px 24px;
}

.edit-form .form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.edit-form .form-row:last-child {
  margin-bottom: 0;
}

.edit-form .full-width {
  width: 100%;
}

.edit-form .half-width {
  width: calc(50% - 8px);
}

/* Modal Footer Button Styling */
.modal-footer button {
  margin-left: 8px;
}

.modal-footer button:first-child {
  margin-left: 0;
}

.modal-footer button mat-icon {
  margin-right: 8px;
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .header-section {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 16px;
    margin-bottom: 20px;
  }

  .title-section h2 {
    font-size: 24px;
  }

  .title-section p {
    font-size: 14px;
  }

  .action-buttons {
    flex-direction: row;
    justify-content: center;
    width: 100%;
    gap: 10px;
  }

  .action-buttons button {
    min-width: 140px;
    height: 40px;
    font-size: 13px;
  }

  .content-section {
    padding: 16px;
  }

  .search-field {
    width: 100%;
    min-width: unset;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .payees-table {
    min-width: 700px;
  }
  .action-buttons {
    min-width: 120px;
    gap: 2px;
  }

  .action-buttons .action-btn {
    width: 28px;
    height: 26px;
    min-width: 28px;
  }

  .action-buttons .action-btn mat-icon {
    font-size: 14px;
    width: 14px;
    height: 14px;
  }

  .modal-content {
    width: 95%;
    margin: 10px;
    max-height: 95vh;
  }

  .modal-header {
    padding: 16px;
  }

  .modal-body {
    padding: 16px;
  }

  .modal-footer {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
  }

  .modal-footer button {
    width: 100%;
    margin-left: 0;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .half-width {
    width: 100%;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .view-details-modal {
    width: 98%;
    margin: 10px;
  }

  .edit-form .form-row {
    flex-direction: column;
  }

  .edit-form .half-width {
    width: 100%;
  }

  /* Table cell adjustments for mobile */
  .payees-table .mat-mdc-cell,
  .payees-table .mat-mdc-header-cell {
    padding: 8px 4px;
    font-size: 0.9rem;
  }

  .status-badge {
    font-size: 0.7rem;
    padding: 2px 8px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 8px;
  }

  .header-section {
    padding: 12px;
    margin-bottom: 16px;
  }

  .title-section h2 {
    font-size: 20px;
  }

  .title-section p {
    font-size: 13px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons button {
    width: 100%;
    min-width: unset;
    height: 36px;
    font-size: 12px;
  }

  .content-section {
    padding: 12px;
  }

  .modal-content {
    width: 98%;
    margin: 5px;
  }

  .modal-header {
    padding: 12px;
  }

  .modal-header h3 {
    font-size: 18px;
  }

  .modal-body {
    padding: 12px;
  }

  .modal-footer {
    padding: 10px 12px;
  }

  .action-buttons .action-btn {
    width: 26px;
    height: 24px;
    min-width: 26px;
  }

  .action-buttons .action-btn mat-icon {
    font-size: 12px;
    width: 12px;
    height: 12px;
  }

  .payees-table {
    min-width: 600px;
  }

  .payees-table .mat-mdc-cell,
  .payees-table .mat-mdc-header-cell {
    padding: 6px 2px;
    font-size: 0.8rem;
  }
}
