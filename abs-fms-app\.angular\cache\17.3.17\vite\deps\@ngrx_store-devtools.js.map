{"version": 3, "sources": ["../../../../../node_modules/@ngrx/store-devtools/fesm2022/ngrx-store-devtools.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, NgZone, Injectable, Inject, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport * as i2 from '@ngrx/store';\nimport { ActionsSubject, UPDATE, INIT, INITIAL_STATE, StateObservable, ReducerManagerDispatcher } from '@ngrx/store';\nimport { EMPTY, Observable, of, merge, queueScheduler, ReplaySubject } from 'rxjs';\nimport { share, filter, map, concatMap, timeout, debounceTime, catchError, take, takeUntil, switchMap, skip, observeOn, withLatestFrom, scan } from 'rxjs/operators';\nimport { toSignal } from '@angular/core/rxjs-interop';\nconst PERFORM_ACTION = 'PERFORM_ACTION';\nconst REFRESH = 'REFRESH';\nconst RESET = 'RESET';\nconst ROLLBACK = 'ROLLBACK';\nconst COMMIT = 'COMMIT';\nconst SWEEP = 'SWEEP';\nconst TOGGLE_ACTION = 'TOGGLE_ACTION';\nconst SET_ACTIONS_ACTIVE = 'SET_ACTIONS_ACTIVE';\nconst JUMP_TO_STATE = 'JUMP_TO_STATE';\nconst JUMP_TO_ACTION = 'JUMP_TO_ACTION';\nconst IMPORT_STATE = 'IMPORT_STATE';\nconst LOCK_CHANGES = 'LOCK_CHANGES';\nconst PAUSE_RECORDING = 'PAUSE_RECORDING';\nclass PerformAction {\n  constructor(action, timestamp) {\n    this.action = action;\n    this.timestamp = timestamp;\n    this.type = PERFORM_ACTION;\n    if (typeof action.type === 'undefined') {\n      throw new Error('Actions may not have an undefined \"type\" property. ' + 'Have you misspelled a constant?');\n    }\n  }\n}\nclass Refresh {\n  constructor() {\n    this.type = REFRESH;\n  }\n}\nclass Reset {\n  constructor(timestamp) {\n    this.timestamp = timestamp;\n    this.type = RESET;\n  }\n}\nclass Rollback {\n  constructor(timestamp) {\n    this.timestamp = timestamp;\n    this.type = ROLLBACK;\n  }\n}\nclass Commit {\n  constructor(timestamp) {\n    this.timestamp = timestamp;\n    this.type = COMMIT;\n  }\n}\nclass Sweep {\n  constructor() {\n    this.type = SWEEP;\n  }\n}\nclass ToggleAction {\n  constructor(id) {\n    this.id = id;\n    this.type = TOGGLE_ACTION;\n  }\n}\nclass SetActionsActive {\n  constructor(start, end, active = true) {\n    this.start = start;\n    this.end = end;\n    this.active = active;\n    this.type = SET_ACTIONS_ACTIVE;\n  }\n}\nclass JumpToState {\n  constructor(index) {\n    this.index = index;\n    this.type = JUMP_TO_STATE;\n  }\n}\nclass JumpToAction {\n  constructor(actionId) {\n    this.actionId = actionId;\n    this.type = JUMP_TO_ACTION;\n  }\n}\nclass ImportState {\n  constructor(nextLiftedState) {\n    this.nextLiftedState = nextLiftedState;\n    this.type = IMPORT_STATE;\n  }\n}\nclass LockChanges {\n  constructor(status) {\n    this.status = status;\n    this.type = LOCK_CHANGES;\n  }\n}\nclass PauseRecording {\n  constructor(status) {\n    this.status = status;\n    this.type = PAUSE_RECORDING;\n  }\n}\n\n/**\n * Chrome extension documentation\n * @see https://github.com/reduxjs/redux-devtools/blob/main/extension/docs/API/Arguments.md\n * Firefox extension documentation\n * @see https://github.com/zalmoxisus/redux-devtools-extension/blob/master/docs/API/Arguments.md\n */\nclass StoreDevtoolsConfig {\n  constructor() {\n    /**\n     * Maximum allowed actions to be stored in the history tree (default: `false`)\n     */\n    this.maxAge = false;\n  }\n}\nconst STORE_DEVTOOLS_CONFIG = new InjectionToken('@ngrx/store-devtools Options');\n/**\n * Used to provide a `StoreDevtoolsConfig` for the store-devtools.\n */\nconst INITIAL_OPTIONS = new InjectionToken('@ngrx/store-devtools Initial Config');\nfunction noMonitor() {\n  return null;\n}\nconst DEFAULT_NAME = 'NgRx Store DevTools';\nfunction createConfig(optionsInput) {\n  const DEFAULT_OPTIONS = {\n    maxAge: false,\n    monitor: noMonitor,\n    actionSanitizer: undefined,\n    stateSanitizer: undefined,\n    name: DEFAULT_NAME,\n    serialize: false,\n    logOnly: false,\n    autoPause: false,\n    trace: false,\n    traceLimit: 75,\n    // Add all features explicitly. This prevent buggy behavior for\n    // options like \"lock\" which might otherwise not show up.\n    features: {\n      pause: true,\n      // Start/pause recording of dispatched actions\n      lock: true,\n      // Lock/unlock dispatching actions and side effects\n      persist: true,\n      // Persist states on page reloading\n      export: true,\n      // Export history of actions in a file\n      import: 'custom',\n      // Import history of actions from a file\n      jump: true,\n      // Jump back and forth (time travelling)\n      skip: true,\n      // Skip (cancel) actions\n      reorder: true,\n      // Drag and drop actions in the history list\n      dispatch: true,\n      // Dispatch custom actions or action creators\n      test: true // Generate tests for the selected actions\n    },\n    connectInZone: false\n  };\n  const options = typeof optionsInput === 'function' ? optionsInput() : optionsInput;\n  const logOnly = options.logOnly ? {\n    pause: true,\n    export: true,\n    test: true\n  } : false;\n  const features = options.features || logOnly || DEFAULT_OPTIONS.features;\n  if (features.import === true) {\n    features.import = 'custom';\n  }\n  const config = Object.assign({}, DEFAULT_OPTIONS, {\n    features\n  }, options);\n  if (config.maxAge && config.maxAge < 2) {\n    throw new Error(`Devtools 'maxAge' cannot be less than 2, got ${config.maxAge}`);\n  }\n  return config;\n}\nfunction difference(first, second) {\n  return first.filter(item => second.indexOf(item) < 0);\n}\n/**\n * Provides an app's view into the state of the lifted store.\n */\nfunction unliftState(liftedState) {\n  const {\n    computedStates,\n    currentStateIndex\n  } = liftedState;\n  // At start up NgRx dispatches init actions,\n  // When these init actions are being filtered out by the predicate or safe/block list options\n  // we don't have a complete computed states yet.\n  // At this point it could happen that we're out of bounds, when this happens we fall back to the last known state\n  if (currentStateIndex >= computedStates.length) {\n    const {\n      state\n    } = computedStates[computedStates.length - 1];\n    return state;\n  }\n  const {\n    state\n  } = computedStates[currentStateIndex];\n  return state;\n}\nfunction unliftAction(liftedState) {\n  return liftedState.actionsById[liftedState.nextActionId - 1];\n}\n/**\n * Lifts an app's action into an action on the lifted store.\n */\nfunction liftAction(action) {\n  return new PerformAction(action, +Date.now());\n}\n/**\n * Sanitizes given actions with given function.\n */\nfunction sanitizeActions(actionSanitizer, actions) {\n  return Object.keys(actions).reduce((sanitizedActions, actionIdx) => {\n    const idx = Number(actionIdx);\n    sanitizedActions[idx] = sanitizeAction(actionSanitizer, actions[idx], idx);\n    return sanitizedActions;\n  }, {});\n}\n/**\n * Sanitizes given action with given function.\n */\nfunction sanitizeAction(actionSanitizer, action, actionIdx) {\n  return {\n    ...action,\n    action: actionSanitizer(action.action, actionIdx)\n  };\n}\n/**\n * Sanitizes given states with given function.\n */\nfunction sanitizeStates(stateSanitizer, states) {\n  return states.map((computedState, idx) => ({\n    state: sanitizeState(stateSanitizer, computedState.state, idx),\n    error: computedState.error\n  }));\n}\n/**\n * Sanitizes given state with given function.\n */\nfunction sanitizeState(stateSanitizer, state, stateIdx) {\n  return stateSanitizer(state, stateIdx);\n}\n/**\n * Read the config and tell if actions should be filtered\n */\nfunction shouldFilterActions(config) {\n  return config.predicate || config.actionsSafelist || config.actionsBlocklist;\n}\n/**\n * Return a full filtered lifted state\n */\nfunction filterLiftedState(liftedState, predicate, safelist, blocklist) {\n  const filteredStagedActionIds = [];\n  const filteredActionsById = {};\n  const filteredComputedStates = [];\n  liftedState.stagedActionIds.forEach((id, idx) => {\n    const liftedAction = liftedState.actionsById[id];\n    if (!liftedAction) return;\n    if (idx && isActionFiltered(liftedState.computedStates[idx], liftedAction, predicate, safelist, blocklist)) {\n      return;\n    }\n    filteredActionsById[id] = liftedAction;\n    filteredStagedActionIds.push(id);\n    filteredComputedStates.push(liftedState.computedStates[idx]);\n  });\n  return {\n    ...liftedState,\n    stagedActionIds: filteredStagedActionIds,\n    actionsById: filteredActionsById,\n    computedStates: filteredComputedStates\n  };\n}\n/**\n * Return true is the action should be ignored\n */\nfunction isActionFiltered(state, action, predicate, safelist, blockedlist) {\n  const predicateMatch = predicate && !predicate(state, action.action);\n  const safelistMatch = safelist && !action.action.type.match(safelist.map(s => escapeRegExp(s)).join('|'));\n  const blocklistMatch = blockedlist && action.action.type.match(blockedlist.map(s => escapeRegExp(s)).join('|'));\n  return predicateMatch || safelistMatch || blocklistMatch;\n}\n/**\n * Return string with escaped RegExp special characters\n * https://stackoverflow.com/a/6969486/1337347\n */\nfunction escapeRegExp(s) {\n  return s.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\nfunction injectZoneConfig(connectInZone) {\n  const ngZone = connectInZone ? inject(NgZone) : null;\n  return {\n    ngZone,\n    connectInZone\n  };\n}\nclass DevtoolsDispatcher extends ActionsSubject {\n  /** @nocollapse */static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵDevtoolsDispatcher_BaseFactory;\n      return function DevtoolsDispatcher_Factory(t) {\n        return (ɵDevtoolsDispatcher_BaseFactory || (ɵDevtoolsDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(DevtoolsDispatcher)))(t || DevtoolsDispatcher);\n      };\n    })();\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DevtoolsDispatcher,\n      factory: DevtoolsDispatcher.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DevtoolsDispatcher, [{\n    type: Injectable\n  }], null, null);\n})();\nconst ExtensionActionTypes = {\n  START: 'START',\n  DISPATCH: 'DISPATCH',\n  STOP: 'STOP',\n  ACTION: 'ACTION'\n};\nconst REDUX_DEVTOOLS_EXTENSION = new InjectionToken('@ngrx/store-devtools Redux Devtools Extension');\nclass DevtoolsExtension {\n  constructor(devtoolsExtension, config, dispatcher) {\n    this.config = config;\n    this.dispatcher = dispatcher;\n    this.zoneConfig = injectZoneConfig(this.config.connectInZone);\n    this.devtoolsExtension = devtoolsExtension;\n    this.createActionStreams();\n  }\n  notify(action, state) {\n    if (!this.devtoolsExtension) {\n      return;\n    }\n    // Check to see if the action requires a full update of the liftedState.\n    // If it is a simple action generated by the user's app and the recording\n    // is not locked/paused, only send the action and the current state (fast).\n    //\n    // A full liftedState update (slow: serializes the entire liftedState) is\n    // only required when:\n    //   a) redux-devtools-extension fires the @@Init action (ignored by\n    //      @ngrx/store-devtools)\n    //   b) an action is generated by an @ngrx module (e.g. @ngrx/effects/init\n    //      or @ngrx/store/update-reducers)\n    //   c) the state has been recomputed due to time-traveling\n    //   d) any action that is not a PerformAction to err on the side of\n    //      caution.\n    if (action.type === PERFORM_ACTION) {\n      if (state.isLocked || state.isPaused) {\n        return;\n      }\n      const currentState = unliftState(state);\n      if (shouldFilterActions(this.config) && isActionFiltered(currentState, action, this.config.predicate, this.config.actionsSafelist, this.config.actionsBlocklist)) {\n        return;\n      }\n      const sanitizedState = this.config.stateSanitizer ? sanitizeState(this.config.stateSanitizer, currentState, state.currentStateIndex) : currentState;\n      const sanitizedAction = this.config.actionSanitizer ? sanitizeAction(this.config.actionSanitizer, action, state.nextActionId) : action;\n      this.sendToReduxDevtools(() => this.extensionConnection.send(sanitizedAction, sanitizedState));\n    } else {\n      // Requires full state update\n      const sanitizedLiftedState = {\n        ...state,\n        stagedActionIds: state.stagedActionIds,\n        actionsById: this.config.actionSanitizer ? sanitizeActions(this.config.actionSanitizer, state.actionsById) : state.actionsById,\n        computedStates: this.config.stateSanitizer ? sanitizeStates(this.config.stateSanitizer, state.computedStates) : state.computedStates\n      };\n      this.sendToReduxDevtools(() => this.devtoolsExtension.send(null, sanitizedLiftedState, this.getExtensionConfig(this.config)));\n    }\n  }\n  createChangesObservable() {\n    if (!this.devtoolsExtension) {\n      return EMPTY;\n    }\n    return new Observable(subscriber => {\n      const connection = this.zoneConfig.connectInZone ?\n      // To reduce change detection cycles, we need to run the `connect` method\n      // outside of the Angular zone. The `connect` method adds a `message`\n      // event listener to communicate with an extension using `window.postMessage`\n      // and handle message events.\n      this.zoneConfig.ngZone.runOutsideAngular(() => this.devtoolsExtension.connect(this.getExtensionConfig(this.config))) : this.devtoolsExtension.connect(this.getExtensionConfig(this.config));\n      this.extensionConnection = connection;\n      connection.init();\n      connection.subscribe(change => subscriber.next(change));\n      return connection.unsubscribe;\n    });\n  }\n  createActionStreams() {\n    // Listens to all changes\n    const changes$ = this.createChangesObservable().pipe(share());\n    // Listen for the start action\n    const start$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.START));\n    // Listen for the stop action\n    const stop$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.STOP));\n    // Listen for lifted actions\n    const liftedActions$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.DISPATCH), map(change => this.unwrapAction(change.payload)), concatMap(action => {\n      if (action.type === IMPORT_STATE) {\n        // State imports may happen in two situations:\n        // 1. Explicitly by user\n        // 2. User activated the \"persist state accross reloads\" option\n        //    and now the state is imported during reload.\n        // Because of option 2, we need to give possible\n        // lazy loaded reducers time to instantiate.\n        // As soon as there is no UPDATE action within 1 second,\n        // it is assumed that all reducers are loaded.\n        return this.dispatcher.pipe(filter(action => action.type === UPDATE), timeout(1000), debounceTime(1000), map(() => action), catchError(() => of(action)), take(1));\n      } else {\n        return of(action);\n      }\n    }));\n    // Listen for unlifted actions\n    const actions$ = changes$.pipe(filter(change => change.type === ExtensionActionTypes.ACTION), map(change => this.unwrapAction(change.payload)));\n    const actionsUntilStop$ = actions$.pipe(takeUntil(stop$));\n    const liftedUntilStop$ = liftedActions$.pipe(takeUntil(stop$));\n    this.start$ = start$.pipe(takeUntil(stop$));\n    // Only take the action sources between the start/stop events\n    this.actions$ = this.start$.pipe(switchMap(() => actionsUntilStop$));\n    this.liftedActions$ = this.start$.pipe(switchMap(() => liftedUntilStop$));\n  }\n  unwrapAction(action) {\n    // indirect eval according to https://esbuild.github.io/content-types/#direct-eval\n    return typeof action === 'string' ? (0, eval)(`(${action})`) : action;\n  }\n  getExtensionConfig(config) {\n    const extensionOptions = {\n      name: config.name,\n      features: config.features,\n      serialize: config.serialize,\n      autoPause: config.autoPause ?? false,\n      trace: config.trace ?? false,\n      traceLimit: config.traceLimit ?? 75\n      // The action/state sanitizers are not added to the config\n      // because sanitation is done in this class already.\n      // It is done before sending it to the devtools extension for consistency:\n      // - If we call extensionConnection.send(...),\n      //   the extension would call the sanitizers.\n      // - If we call devtoolsExtension.send(...) (aka full state update),\n      //   the extension would NOT call the sanitizers, so we have to do it ourselves.\n    };\n    if (config.maxAge !== false /* support === 0 */) {\n      extensionOptions.maxAge = config.maxAge;\n    }\n    return extensionOptions;\n  }\n  sendToReduxDevtools(send) {\n    try {\n      send();\n    } catch (err) {\n      console.warn('@ngrx/store-devtools: something went wrong inside the redux devtools', err);\n    }\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function DevtoolsExtension_Factory(t) {\n      return new (t || DevtoolsExtension)(i0.ɵɵinject(REDUX_DEVTOOLS_EXTENSION), i0.ɵɵinject(STORE_DEVTOOLS_CONFIG), i0.ɵɵinject(DevtoolsDispatcher));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DevtoolsExtension,\n      factory: DevtoolsExtension.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DevtoolsExtension, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [REDUX_DEVTOOLS_EXTENSION]\n    }]\n  }, {\n    type: StoreDevtoolsConfig,\n    decorators: [{\n      type: Inject,\n      args: [STORE_DEVTOOLS_CONFIG]\n    }]\n  }, {\n    type: DevtoolsDispatcher\n  }], null);\n})();\nconst INIT_ACTION = {\n  type: INIT\n};\nconst RECOMPUTE = '@ngrx/store-devtools/recompute';\nconst RECOMPUTE_ACTION = {\n  type: RECOMPUTE\n};\n/**\n * Computes the next entry in the log by applying an action.\n */\nfunction computeNextEntry(reducer, action, state, error, errorHandler) {\n  if (error) {\n    return {\n      state,\n      error: 'Interrupted by an error up the chain'\n    };\n  }\n  let nextState = state;\n  let nextError;\n  try {\n    nextState = reducer(state, action);\n  } catch (err) {\n    nextError = err.toString();\n    errorHandler.handleError(err);\n  }\n  return {\n    state: nextState,\n    error: nextError\n  };\n}\n/**\n * Runs the reducer on invalidated actions to get a fresh computation log.\n */\nfunction recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused) {\n  // Optimization: exit early and return the same reference\n  // if we know nothing could have changed.\n  if (minInvalidatedStateIndex >= computedStates.length && computedStates.length === stagedActionIds.length) {\n    return computedStates;\n  }\n  const nextComputedStates = computedStates.slice(0, minInvalidatedStateIndex);\n  // If the recording is paused, recompute all states up until the pause state,\n  // else recompute all states.\n  const lastIncludedActionId = stagedActionIds.length - (isPaused ? 1 : 0);\n  for (let i = minInvalidatedStateIndex; i < lastIncludedActionId; i++) {\n    const actionId = stagedActionIds[i];\n    const action = actionsById[actionId].action;\n    const previousEntry = nextComputedStates[i - 1];\n    const previousState = previousEntry ? previousEntry.state : committedState;\n    const previousError = previousEntry ? previousEntry.error : undefined;\n    const shouldSkip = skippedActionIds.indexOf(actionId) > -1;\n    const entry = shouldSkip ? previousEntry : computeNextEntry(reducer, action, previousState, previousError, errorHandler);\n    nextComputedStates.push(entry);\n  }\n  // If the recording is paused, the last state will not be recomputed,\n  // because it's essentially not part of the state history.\n  if (isPaused) {\n    nextComputedStates.push(computedStates[computedStates.length - 1]);\n  }\n  return nextComputedStates;\n}\nfunction liftInitialState(initialCommittedState, monitorReducer) {\n  return {\n    monitorState: monitorReducer(undefined, {}),\n    nextActionId: 1,\n    actionsById: {\n      0: liftAction(INIT_ACTION)\n    },\n    stagedActionIds: [0],\n    skippedActionIds: [],\n    committedState: initialCommittedState,\n    currentStateIndex: 0,\n    computedStates: [],\n    isLocked: false,\n    isPaused: false\n  };\n}\n/**\n * Creates a history state reducer from an app's reducer.\n */\nfunction liftReducerWith(initialCommittedState, initialLiftedState, errorHandler, monitorReducer, options = {}) {\n  /**\n   * Manages how the history actions modify the history state.\n   */\n  return reducer => (liftedState, liftedAction) => {\n    let {\n      monitorState,\n      actionsById,\n      nextActionId,\n      stagedActionIds,\n      skippedActionIds,\n      committedState,\n      currentStateIndex,\n      computedStates,\n      isLocked,\n      isPaused\n    } = liftedState || initialLiftedState;\n    if (!liftedState) {\n      // Prevent mutating initialLiftedState\n      actionsById = Object.create(actionsById);\n    }\n    function commitExcessActions(n) {\n      // Auto-commits n-number of excess actions.\n      let excess = n;\n      let idsToDelete = stagedActionIds.slice(1, excess + 1);\n      for (let i = 0; i < idsToDelete.length; i++) {\n        if (computedStates[i + 1].error) {\n          // Stop if error is found. Commit actions up to error.\n          excess = i;\n          idsToDelete = stagedActionIds.slice(1, excess + 1);\n          break;\n        } else {\n          delete actionsById[idsToDelete[i]];\n        }\n      }\n      skippedActionIds = skippedActionIds.filter(id => idsToDelete.indexOf(id) === -1);\n      stagedActionIds = [0, ...stagedActionIds.slice(excess + 1)];\n      committedState = computedStates[excess].state;\n      computedStates = computedStates.slice(excess);\n      currentStateIndex = currentStateIndex > excess ? currentStateIndex - excess : 0;\n    }\n    function commitChanges() {\n      // Consider the last committed state the new starting point.\n      // Squash any staged actions into a single committed state.\n      actionsById = {\n        0: liftAction(INIT_ACTION)\n      };\n      nextActionId = 1;\n      stagedActionIds = [0];\n      skippedActionIds = [];\n      committedState = computedStates[currentStateIndex].state;\n      currentStateIndex = 0;\n      computedStates = [];\n    }\n    // By default, aggressively recompute every state whatever happens.\n    // This has O(n) performance, so we'll override this to a sensible\n    // value whenever we feel like we don't have to recompute the states.\n    let minInvalidatedStateIndex = 0;\n    switch (liftedAction.type) {\n      case LOCK_CHANGES:\n        {\n          isLocked = liftedAction.status;\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n      case PAUSE_RECORDING:\n        {\n          isPaused = liftedAction.status;\n          if (isPaused) {\n            // Add a pause action to signal the devtools-user the recording is paused.\n            // The corresponding state will be overwritten on each update to always contain\n            // the latest state (see Actions.PERFORM_ACTION).\n            stagedActionIds = [...stagedActionIds, nextActionId];\n            actionsById[nextActionId] = new PerformAction({\n              type: '@ngrx/devtools/pause'\n            }, +Date.now());\n            nextActionId++;\n            minInvalidatedStateIndex = stagedActionIds.length - 1;\n            computedStates = computedStates.concat(computedStates[computedStates.length - 1]);\n            if (currentStateIndex === stagedActionIds.length - 2) {\n              currentStateIndex++;\n            }\n            minInvalidatedStateIndex = Infinity;\n          } else {\n            commitChanges();\n          }\n          break;\n        }\n      case RESET:\n        {\n          // Get back to the state the store was created with.\n          actionsById = {\n            0: liftAction(INIT_ACTION)\n          };\n          nextActionId = 1;\n          stagedActionIds = [0];\n          skippedActionIds = [];\n          committedState = initialCommittedState;\n          currentStateIndex = 0;\n          computedStates = [];\n          break;\n        }\n      case COMMIT:\n        {\n          commitChanges();\n          break;\n        }\n      case ROLLBACK:\n        {\n          // Forget about any staged actions.\n          // Start again from the last committed state.\n          actionsById = {\n            0: liftAction(INIT_ACTION)\n          };\n          nextActionId = 1;\n          stagedActionIds = [0];\n          skippedActionIds = [];\n          currentStateIndex = 0;\n          computedStates = [];\n          break;\n        }\n      case TOGGLE_ACTION:\n        {\n          // Toggle whether an action with given ID is skipped.\n          // Being skipped means it is a no-op during the computation.\n          const {\n            id: actionId\n          } = liftedAction;\n          const index = skippedActionIds.indexOf(actionId);\n          if (index === -1) {\n            skippedActionIds = [actionId, ...skippedActionIds];\n          } else {\n            skippedActionIds = skippedActionIds.filter(id => id !== actionId);\n          }\n          // Optimization: we know history before this action hasn't changed\n          minInvalidatedStateIndex = stagedActionIds.indexOf(actionId);\n          break;\n        }\n      case SET_ACTIONS_ACTIVE:\n        {\n          // Toggle whether an action with given ID is skipped.\n          // Being skipped means it is a no-op during the computation.\n          const {\n            start,\n            end,\n            active\n          } = liftedAction;\n          const actionIds = [];\n          for (let i = start; i < end; i++) actionIds.push(i);\n          if (active) {\n            skippedActionIds = difference(skippedActionIds, actionIds);\n          } else {\n            skippedActionIds = [...skippedActionIds, ...actionIds];\n          }\n          // Optimization: we know history before this action hasn't changed\n          minInvalidatedStateIndex = stagedActionIds.indexOf(start);\n          break;\n        }\n      case JUMP_TO_STATE:\n        {\n          // Without recomputing anything, move the pointer that tell us\n          // which state is considered the current one. Useful for sliders.\n          currentStateIndex = liftedAction.index;\n          // Optimization: we know the history has not changed.\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n      case JUMP_TO_ACTION:\n        {\n          // Jumps to a corresponding state to a specific action.\n          // Useful when filtering actions.\n          const index = stagedActionIds.indexOf(liftedAction.actionId);\n          if (index !== -1) currentStateIndex = index;\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n      case SWEEP:\n        {\n          // Forget any actions that are currently being skipped.\n          stagedActionIds = difference(stagedActionIds, skippedActionIds);\n          skippedActionIds = [];\n          currentStateIndex = Math.min(currentStateIndex, stagedActionIds.length - 1);\n          break;\n        }\n      case PERFORM_ACTION:\n        {\n          // Ignore action and return state as is if recording is locked\n          if (isLocked) {\n            return liftedState || initialLiftedState;\n          }\n          if (isPaused || liftedState && isActionFiltered(liftedState.computedStates[currentStateIndex], liftedAction, options.predicate, options.actionsSafelist, options.actionsBlocklist)) {\n            // If recording is paused or if the action should be ignored, overwrite the last state\n            // (corresponds to the pause action) and keep everything else as is.\n            // This way, the app gets the new current state while the devtools\n            // do not record another action.\n            const lastState = computedStates[computedStates.length - 1];\n            computedStates = [...computedStates.slice(0, -1), computeNextEntry(reducer, liftedAction.action, lastState.state, lastState.error, errorHandler)];\n            minInvalidatedStateIndex = Infinity;\n            break;\n          }\n          // Auto-commit as new actions come in.\n          if (options.maxAge && stagedActionIds.length === options.maxAge) {\n            commitExcessActions(1);\n          }\n          if (currentStateIndex === stagedActionIds.length - 1) {\n            currentStateIndex++;\n          }\n          const actionId = nextActionId++;\n          // Mutation! This is the hottest path, and we optimize on purpose.\n          // It is safe because we set a new key in a cache dictionary.\n          actionsById[actionId] = liftedAction;\n          stagedActionIds = [...stagedActionIds, actionId];\n          // Optimization: we know that only the new action needs computing.\n          minInvalidatedStateIndex = stagedActionIds.length - 1;\n          break;\n        }\n      case IMPORT_STATE:\n        {\n          // Completely replace everything.\n          ({\n            monitorState,\n            actionsById,\n            nextActionId,\n            stagedActionIds,\n            skippedActionIds,\n            committedState,\n            currentStateIndex,\n            computedStates,\n            isLocked,\n            isPaused\n          } = liftedAction.nextLiftedState);\n          break;\n        }\n      case INIT:\n        {\n          // Always recompute states on hot reload and init.\n          minInvalidatedStateIndex = 0;\n          if (options.maxAge && stagedActionIds.length > options.maxAge) {\n            // States must be recomputed before committing excess.\n            computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n            commitExcessActions(stagedActionIds.length - options.maxAge);\n            // Avoid double computation.\n            minInvalidatedStateIndex = Infinity;\n          }\n          break;\n        }\n      case UPDATE:\n        {\n          const stateHasErrors = computedStates.filter(state => state.error).length > 0;\n          if (stateHasErrors) {\n            // Recompute all states\n            minInvalidatedStateIndex = 0;\n            if (options.maxAge && stagedActionIds.length > options.maxAge) {\n              // States must be recomputed before committing excess.\n              computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n              commitExcessActions(stagedActionIds.length - options.maxAge);\n              // Avoid double computation.\n              minInvalidatedStateIndex = Infinity;\n            }\n          } else {\n            // If not paused/locked, add a new action to signal devtools-user\n            // that there was a reducer update.\n            if (!isPaused && !isLocked) {\n              if (currentStateIndex === stagedActionIds.length - 1) {\n                currentStateIndex++;\n              }\n              // Add a new action to only recompute state\n              const actionId = nextActionId++;\n              actionsById[actionId] = new PerformAction(liftedAction, +Date.now());\n              stagedActionIds = [...stagedActionIds, actionId];\n              minInvalidatedStateIndex = stagedActionIds.length - 1;\n              computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n            }\n            // Recompute state history with latest reducer and update action\n            computedStates = computedStates.map(cmp => ({\n              ...cmp,\n              state: reducer(cmp.state, RECOMPUTE_ACTION)\n            }));\n            currentStateIndex = stagedActionIds.length - 1;\n            if (options.maxAge && stagedActionIds.length > options.maxAge) {\n              commitExcessActions(stagedActionIds.length - options.maxAge);\n            }\n            // Avoid double computation.\n            minInvalidatedStateIndex = Infinity;\n          }\n          break;\n        }\n      default:\n        {\n          // If the action is not recognized, it's a monitor action.\n          // Optimization: a monitor action can't change history.\n          minInvalidatedStateIndex = Infinity;\n          break;\n        }\n    }\n    computedStates = recomputeStates(computedStates, minInvalidatedStateIndex, reducer, committedState, actionsById, stagedActionIds, skippedActionIds, errorHandler, isPaused);\n    monitorState = monitorReducer(monitorState, liftedAction);\n    return {\n      monitorState,\n      actionsById,\n      nextActionId,\n      stagedActionIds,\n      skippedActionIds,\n      committedState,\n      currentStateIndex,\n      computedStates,\n      isLocked,\n      isPaused\n    };\n  };\n}\nclass StoreDevtools {\n  constructor(dispatcher, actions$, reducers$, extension, scannedActions, errorHandler, initialState, config) {\n    const liftedInitialState = liftInitialState(initialState, config.monitor);\n    const liftReducer = liftReducerWith(initialState, liftedInitialState, errorHandler, config.monitor, config);\n    const liftedAction$ = merge(merge(actions$.asObservable().pipe(skip(1)), extension.actions$).pipe(map(liftAction)), dispatcher, extension.liftedActions$).pipe(observeOn(queueScheduler));\n    const liftedReducer$ = reducers$.pipe(map(liftReducer));\n    const zoneConfig = injectZoneConfig(config.connectInZone);\n    const liftedStateSubject = new ReplaySubject(1);\n    this.liftedStateSubscription = liftedAction$.pipe(withLatestFrom(liftedReducer$),\n    // The extension would post messages back outside of the Angular zone\n    // because we call `connect()` wrapped with `runOutsideAngular`. We run change\n    // detection only once at the end after all the required asynchronous tasks have\n    // been processed (for instance, `setInterval` scheduled by the `timeout` operator).\n    // We have to re-enter the Angular zone before the `scan` since it runs the reducer\n    // which must be run within the Angular zone.\n    emitInZone(zoneConfig), scan(({\n      state: liftedState\n    }, [action, reducer]) => {\n      let reducedLiftedState = reducer(liftedState, action);\n      // On full state update\n      // If we have actions filters, we must filter completely our lifted state to be sync with the extension\n      if (action.type !== PERFORM_ACTION && shouldFilterActions(config)) {\n        reducedLiftedState = filterLiftedState(reducedLiftedState, config.predicate, config.actionsSafelist, config.actionsBlocklist);\n      }\n      // Extension should be sent the sanitized lifted state\n      extension.notify(action, reducedLiftedState);\n      return {\n        state: reducedLiftedState,\n        action\n      };\n    }, {\n      state: liftedInitialState,\n      action: null\n    })).subscribe(({\n      state,\n      action\n    }) => {\n      liftedStateSubject.next(state);\n      if (action.type === PERFORM_ACTION) {\n        const unliftedAction = action.action;\n        scannedActions.next(unliftedAction);\n      }\n    });\n    this.extensionStartSubscription = extension.start$.pipe(emitInZone(zoneConfig)).subscribe(() => {\n      this.refresh();\n    });\n    const liftedState$ = liftedStateSubject.asObservable();\n    const state$ = liftedState$.pipe(map(unliftState));\n    Object.defineProperty(state$, 'state', {\n      value: toSignal(state$, {\n        manualCleanup: true,\n        requireSync: true\n      })\n    });\n    this.dispatcher = dispatcher;\n    this.liftedState = liftedState$;\n    this.state = state$;\n  }\n  ngOnDestroy() {\n    // Even though the store devtools plugin is recommended to be\n    // used only in development mode, it can still cause a memory leak\n    // in microfrontend applications that are being created and destroyed\n    // multiple times during development. This results in excessive memory\n    // consumption, as it prevents entire apps from being garbage collected.\n    this.liftedStateSubscription.unsubscribe();\n    this.extensionStartSubscription.unsubscribe();\n  }\n  dispatch(action) {\n    this.dispatcher.next(action);\n  }\n  next(action) {\n    this.dispatcher.next(action);\n  }\n  error(error) {}\n  complete() {}\n  performAction(action) {\n    this.dispatch(new PerformAction(action, +Date.now()));\n  }\n  refresh() {\n    this.dispatch(new Refresh());\n  }\n  reset() {\n    this.dispatch(new Reset(+Date.now()));\n  }\n  rollback() {\n    this.dispatch(new Rollback(+Date.now()));\n  }\n  commit() {\n    this.dispatch(new Commit(+Date.now()));\n  }\n  sweep() {\n    this.dispatch(new Sweep());\n  }\n  toggleAction(id) {\n    this.dispatch(new ToggleAction(id));\n  }\n  jumpToAction(actionId) {\n    this.dispatch(new JumpToAction(actionId));\n  }\n  jumpToState(index) {\n    this.dispatch(new JumpToState(index));\n  }\n  importState(nextLiftedState) {\n    this.dispatch(new ImportState(nextLiftedState));\n  }\n  lockChanges(status) {\n    this.dispatch(new LockChanges(status));\n  }\n  pauseRecording(status) {\n    this.dispatch(new PauseRecording(status));\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function StoreDevtools_Factory(t) {\n      return new (t || StoreDevtools)(i0.ɵɵinject(DevtoolsDispatcher), i0.ɵɵinject(i2.ActionsSubject), i0.ɵɵinject(i2.ReducerObservable), i0.ɵɵinject(DevtoolsExtension), i0.ɵɵinject(i2.ScannedActionsSubject), i0.ɵɵinject(i0.ErrorHandler), i0.ɵɵinject(INITIAL_STATE), i0.ɵɵinject(STORE_DEVTOOLS_CONFIG));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: StoreDevtools,\n      factory: StoreDevtools.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreDevtools, [{\n    type: Injectable\n  }], () => [{\n    type: DevtoolsDispatcher\n  }, {\n    type: i2.ActionsSubject\n  }, {\n    type: i2.ReducerObservable\n  }, {\n    type: DevtoolsExtension\n  }, {\n    type: i2.ScannedActionsSubject\n  }, {\n    type: i0.ErrorHandler\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [INITIAL_STATE]\n    }]\n  }, {\n    type: StoreDevtoolsConfig,\n    decorators: [{\n      type: Inject,\n      args: [STORE_DEVTOOLS_CONFIG]\n    }]\n  }], null);\n})();\n/**\n * If the devtools extension is connected out of the Angular zone,\n * this operator will emit all events within the zone.\n */\nfunction emitInZone({\n  ngZone,\n  connectInZone\n}) {\n  return source => connectInZone ? new Observable(subscriber => source.subscribe({\n    next: value => ngZone.run(() => subscriber.next(value)),\n    error: error => ngZone.run(() => subscriber.error(error)),\n    complete: () => ngZone.run(() => subscriber.complete())\n  })) : source;\n}\nconst IS_EXTENSION_OR_MONITOR_PRESENT = new InjectionToken('@ngrx/store-devtools Is Devtools Extension or Monitor Present');\nfunction createIsExtensionOrMonitorPresent(extension, config) {\n  return Boolean(extension) || config.monitor !== noMonitor;\n}\nfunction createReduxDevtoolsExtension() {\n  const extensionKey = '__REDUX_DEVTOOLS_EXTENSION__';\n  if (typeof window === 'object' && typeof window[extensionKey] !== 'undefined') {\n    return window[extensionKey];\n  } else {\n    return null;\n  }\n}\n/**\n * Provides developer tools and instrumentation for `Store`.\n *\n * @usageNotes\n *\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideStoreDevtools({\n *       maxAge: 25,\n *       logOnly: !isDevMode(),\n *     }),\n *   ],\n * });\n * ```\n */\nfunction provideStoreDevtools(options = {}) {\n  return makeEnvironmentProviders([DevtoolsExtension, DevtoolsDispatcher, StoreDevtools, {\n    provide: INITIAL_OPTIONS,\n    useValue: options\n  }, {\n    provide: IS_EXTENSION_OR_MONITOR_PRESENT,\n    deps: [REDUX_DEVTOOLS_EXTENSION, STORE_DEVTOOLS_CONFIG],\n    useFactory: createIsExtensionOrMonitorPresent\n  }, {\n    provide: REDUX_DEVTOOLS_EXTENSION,\n    useFactory: createReduxDevtoolsExtension\n  }, {\n    provide: STORE_DEVTOOLS_CONFIG,\n    deps: [INITIAL_OPTIONS],\n    useFactory: createConfig\n  }, {\n    provide: StateObservable,\n    deps: [StoreDevtools],\n    useFactory: createStateObservable\n  }, {\n    provide: ReducerManagerDispatcher,\n    useExisting: DevtoolsDispatcher\n  }]);\n}\nfunction createStateObservable(devtools) {\n  return devtools.state;\n}\nclass StoreDevtoolsModule {\n  static instrument(options = {}) {\n    return {\n      ngModule: StoreDevtoolsModule,\n      providers: [provideStoreDevtools(options)]\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function StoreDevtoolsModule_Factory(t) {\n      return new (t || StoreDevtoolsModule)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: StoreDevtoolsModule\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoreDevtoolsModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/**\n * DO NOT EDIT\n *\n * This file is automatically generated at build\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INITIAL_OPTIONS, RECOMPUTE, REDUX_DEVTOOLS_EXTENSION, StoreDevtools, StoreDevtoolsConfig, StoreDevtoolsModule, provideStoreDevtools };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,iBAAiB;AACvB,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,gBAAgB;AACtB,IAAM,qBAAqB;AAC3B,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,QAAQ,WAAW;AAC7B,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,QAAI,OAAO,OAAO,SAAS,aAAa;AACtC,YAAM,IAAI,MAAM,oFAAyF;AAAA,IAC3G;AAAA,EACF;AACF;AACA,IAAM,UAAN,MAAc;AAAA,EACZ,cAAc;AACZ,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,QAAN,MAAY;AAAA,EACV,YAAY,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,WAAN,MAAe;AAAA,EACb,YAAY,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,SAAN,MAAa;AAAA,EACX,YAAY,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,QAAN,MAAY;AAAA,EACV,cAAc;AACZ,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,IAAI;AACd,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EACd;AACF;AASA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,OAAO;AACjB,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,UAAU;AACpB,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,iBAAiB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AACF;AACA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,QAAQ;AAClB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACd;AACF;AAQA,IAAM,sBAAN,MAA0B;AAAA,EACxB,cAAc;AAIZ,SAAK,SAAS;AAAA,EAChB;AACF;AACA,IAAM,wBAAwB,IAAI,eAAe,8BAA8B;AAI/E,IAAM,kBAAkB,IAAI,eAAe,qCAAqC;AAChF,SAAS,YAAY;AACnB,SAAO;AACT;AACA,IAAM,eAAe;AACrB,SAAS,aAAa,cAAc;AAClC,QAAM,kBAAkB;AAAA,IACtB,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA;AAAA;AAAA,IAGZ,UAAU;AAAA,MACR,OAAO;AAAA;AAAA,MAEP,MAAM;AAAA;AAAA,MAEN,SAAS;AAAA;AAAA,MAET,QAAQ;AAAA;AAAA,MAER,QAAQ;AAAA;AAAA,MAER,MAAM;AAAA;AAAA,MAEN,MAAM;AAAA;AAAA,MAEN,SAAS;AAAA;AAAA,MAET,UAAU;AAAA;AAAA,MAEV,MAAM;AAAA;AAAA,IACR;AAAA,IACA,eAAe;AAAA,EACjB;AACA,QAAM,UAAU,OAAO,iBAAiB,aAAa,aAAa,IAAI;AACtE,QAAM,UAAU,QAAQ,UAAU;AAAA,IAChC,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR,IAAI;AACJ,QAAM,WAAW,QAAQ,YAAY,WAAW,gBAAgB;AAChE,MAAI,SAAS,WAAW,MAAM;AAC5B,aAAS,SAAS;AAAA,EACpB;AACA,QAAM,SAAS,OAAO,OAAO,CAAC,GAAG,iBAAiB;AAAA,IAChD;AAAA,EACF,GAAG,OAAO;AACV,MAAI,OAAO,UAAU,OAAO,SAAS,GAAG;AACtC,UAAM,IAAI,MAAM,gDAAgD,OAAO,MAAM,EAAE;AAAA,EACjF;AACA,SAAO;AACT;AACA,SAAS,WAAW,OAAO,QAAQ;AACjC,SAAO,MAAM,OAAO,UAAQ,OAAO,QAAQ,IAAI,IAAI,CAAC;AACtD;AAIA,SAAS,YAAY,aAAa;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AAKJ,MAAI,qBAAqB,eAAe,QAAQ;AAC9C,UAAM;AAAA,MACJ,OAAAA;AAAA,IACF,IAAI,eAAe,eAAe,SAAS,CAAC;AAC5C,WAAOA;AAAA,EACT;AACA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,eAAe,iBAAiB;AACpC,SAAO;AACT;AAOA,SAAS,WAAW,QAAQ;AAC1B,SAAO,IAAI,cAAc,QAAQ,CAAC,KAAK,IAAI,CAAC;AAC9C;AAIA,SAAS,gBAAgB,iBAAiB,SAAS;AACjD,SAAO,OAAO,KAAK,OAAO,EAAE,OAAO,CAAC,kBAAkB,cAAc;AAClE,UAAM,MAAM,OAAO,SAAS;AAC5B,qBAAiB,GAAG,IAAI,eAAe,iBAAiB,QAAQ,GAAG,GAAG,GAAG;AACzE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAIA,SAAS,eAAe,iBAAiB,QAAQ,WAAW;AAC1D,SAAO,iCACF,SADE;AAAA,IAEL,QAAQ,gBAAgB,OAAO,QAAQ,SAAS;AAAA,EAClD;AACF;AAIA,SAAS,eAAe,gBAAgB,QAAQ;AAC9C,SAAO,OAAO,IAAI,CAAC,eAAe,SAAS;AAAA,IACzC,OAAO,cAAc,gBAAgB,cAAc,OAAO,GAAG;AAAA,IAC7D,OAAO,cAAc;AAAA,EACvB,EAAE;AACJ;AAIA,SAAS,cAAc,gBAAgB,OAAO,UAAU;AACtD,SAAO,eAAe,OAAO,QAAQ;AACvC;AAIA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,OAAO,aAAa,OAAO,mBAAmB,OAAO;AAC9D;AAIA,SAAS,kBAAkB,aAAa,WAAW,UAAU,WAAW;AACtE,QAAM,0BAA0B,CAAC;AACjC,QAAM,sBAAsB,CAAC;AAC7B,QAAM,yBAAyB,CAAC;AAChC,cAAY,gBAAgB,QAAQ,CAAC,IAAI,QAAQ;AAC/C,UAAM,eAAe,YAAY,YAAY,EAAE;AAC/C,QAAI,CAAC,aAAc;AACnB,QAAI,OAAO,iBAAiB,YAAY,eAAe,GAAG,GAAG,cAAc,WAAW,UAAU,SAAS,GAAG;AAC1G;AAAA,IACF;AACA,wBAAoB,EAAE,IAAI;AAC1B,4BAAwB,KAAK,EAAE;AAC/B,2BAAuB,KAAK,YAAY,eAAe,GAAG,CAAC;AAAA,EAC7D,CAAC;AACD,SAAO,iCACF,cADE;AAAA,IAEL,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,gBAAgB;AAAA,EAClB;AACF;AAIA,SAAS,iBAAiB,OAAO,QAAQ,WAAW,UAAU,aAAa;AACzE,QAAM,iBAAiB,aAAa,CAAC,UAAU,OAAO,OAAO,MAAM;AACnE,QAAM,gBAAgB,YAAY,CAAC,OAAO,OAAO,KAAK,MAAM,SAAS,IAAI,OAAK,aAAa,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AACxG,QAAM,iBAAiB,eAAe,OAAO,OAAO,KAAK,MAAM,YAAY,IAAI,OAAK,aAAa,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AAC9G,SAAO,kBAAkB,iBAAiB;AAC5C;AAKA,SAAS,aAAa,GAAG;AACvB,SAAO,EAAE,QAAQ,uBAAuB,MAAM;AAChD;AACA,SAAS,iBAAiB,eAAe;AACvC,QAAM,SAAS,gBAAgB,OAAO,MAAM,IAAI;AAChD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,qBAAN,MAAM,4BAA2B,eAAe;AAAA,EAC5B,OAAO;AACvB,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,2BAA2B,GAAG;AAC5C,gBAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,KAAK,mBAAkB;AAAA,MACtJ;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,UAAU;AAAA,EACV,MAAM;AAAA,EACN,QAAQ;AACV;AACA,IAAM,2BAA2B,IAAI,eAAe,+CAA+C;AACnG,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,mBAAmB,QAAQ,YAAY;AACjD,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,aAAa,iBAAiB,KAAK,OAAO,aAAa;AAC5D,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,OAAO,QAAQ,OAAO;AACpB,QAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,IACF;AAcA,QAAI,OAAO,SAAS,gBAAgB;AAClC,UAAI,MAAM,YAAY,MAAM,UAAU;AACpC;AAAA,MACF;AACA,YAAM,eAAe,YAAY,KAAK;AACtC,UAAI,oBAAoB,KAAK,MAAM,KAAK,iBAAiB,cAAc,QAAQ,KAAK,OAAO,WAAW,KAAK,OAAO,iBAAiB,KAAK,OAAO,gBAAgB,GAAG;AAChK;AAAA,MACF;AACA,YAAM,iBAAiB,KAAK,OAAO,iBAAiB,cAAc,KAAK,OAAO,gBAAgB,cAAc,MAAM,iBAAiB,IAAI;AACvI,YAAM,kBAAkB,KAAK,OAAO,kBAAkB,eAAe,KAAK,OAAO,iBAAiB,QAAQ,MAAM,YAAY,IAAI;AAChI,WAAK,oBAAoB,MAAM,KAAK,oBAAoB,KAAK,iBAAiB,cAAc,CAAC;AAAA,IAC/F,OAAO;AAEL,YAAM,uBAAuB,iCACxB,QADwB;AAAA,QAE3B,iBAAiB,MAAM;AAAA,QACvB,aAAa,KAAK,OAAO,kBAAkB,gBAAgB,KAAK,OAAO,iBAAiB,MAAM,WAAW,IAAI,MAAM;AAAA,QACnH,gBAAgB,KAAK,OAAO,iBAAiB,eAAe,KAAK,OAAO,gBAAgB,MAAM,cAAc,IAAI,MAAM;AAAA,MACxH;AACA,WAAK,oBAAoB,MAAM,KAAK,kBAAkB,KAAK,MAAM,sBAAsB,KAAK,mBAAmB,KAAK,MAAM,CAAC,CAAC;AAAA,IAC9H;AAAA,EACF;AAAA,EACA,0BAA0B;AACxB,QAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAO;AAAA,IACT;AACA,WAAO,IAAI,WAAW,gBAAc;AAClC,YAAM,aAAa,KAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,QAKnC,KAAK,WAAW,OAAO,kBAAkB,MAAM,KAAK,kBAAkB,QAAQ,KAAK,mBAAmB,KAAK,MAAM,CAAC,CAAC;AAAA,UAAI,KAAK,kBAAkB,QAAQ,KAAK,mBAAmB,KAAK,MAAM,CAAC;AAC1L,WAAK,sBAAsB;AAC3B,iBAAW,KAAK;AAChB,iBAAW,UAAU,YAAU,WAAW,KAAK,MAAM,CAAC;AACtD,aAAO,WAAW;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AAEpB,UAAM,WAAW,KAAK,wBAAwB,EAAE,KAAK,MAAM,CAAC;AAE5D,UAAM,SAAS,SAAS,KAAK,OAAO,YAAU,OAAO,SAAS,qBAAqB,KAAK,CAAC;AAEzF,UAAM,QAAQ,SAAS,KAAK,OAAO,YAAU,OAAO,SAAS,qBAAqB,IAAI,CAAC;AAEvF,UAAM,iBAAiB,SAAS,KAAK,OAAO,YAAU,OAAO,SAAS,qBAAqB,QAAQ,GAAG,IAAI,YAAU,KAAK,aAAa,OAAO,OAAO,CAAC,GAAG,UAAU,YAAU;AAC1K,UAAI,OAAO,SAAS,cAAc;AAShC,eAAO,KAAK,WAAW,KAAK,OAAO,CAAAC,YAAUA,QAAO,SAAS,MAAM,GAAG,QAAQ,GAAI,GAAG,aAAa,GAAI,GAAG,IAAI,MAAM,MAAM,GAAG,WAAW,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,MACnK,OAAO;AACL,eAAO,GAAG,MAAM;AAAA,MAClB;AAAA,IACF,CAAC,CAAC;AAEF,UAAM,WAAW,SAAS,KAAK,OAAO,YAAU,OAAO,SAAS,qBAAqB,MAAM,GAAG,IAAI,YAAU,KAAK,aAAa,OAAO,OAAO,CAAC,CAAC;AAC9I,UAAM,oBAAoB,SAAS,KAAK,UAAU,KAAK,CAAC;AACxD,UAAM,mBAAmB,eAAe,KAAK,UAAU,KAAK,CAAC;AAC7D,SAAK,SAAS,OAAO,KAAK,UAAU,KAAK,CAAC;AAE1C,SAAK,WAAW,KAAK,OAAO,KAAK,UAAU,MAAM,iBAAiB,CAAC;AACnE,SAAK,iBAAiB,KAAK,OAAO,KAAK,UAAU,MAAM,gBAAgB,CAAC;AAAA,EAC1E;AAAA,EACA,aAAa,QAAQ;AAEnB,WAAO,OAAO,WAAW,YAAY,GAAG,MAAM,IAAI,MAAM,GAAG,IAAI;AAAA,EACjE;AAAA,EACA,mBAAmB,QAAQ;AACzB,UAAM,mBAAmB;AAAA,MACvB,MAAM,OAAO;AAAA,MACb,UAAU,OAAO;AAAA,MACjB,WAAW,OAAO;AAAA,MAClB,WAAW,OAAO,aAAa;AAAA,MAC/B,OAAO,OAAO,SAAS;AAAA,MACvB,YAAY,OAAO,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQnC;AACA,QAAI,OAAO,WAAW,OAA2B;AAC/C,uBAAiB,SAAS,OAAO;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,MAAM;AACxB,QAAI;AACF,WAAK;AAAA,IACP,SAAS,KAAK;AACZ,cAAQ,KAAK,wEAAwE,GAAG;AAAA,IAC1F;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,SAAS,wBAAwB,GAAM,SAAS,qBAAqB,GAAM,SAAS,kBAAkB,CAAC;AAAA,IAChJ;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,IAC7B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,cAAc;AAAA,EAClB,MAAM;AACR;AACA,IAAM,YAAY;AAClB,IAAM,mBAAmB;AAAA,EACvB,MAAM;AACR;AAIA,SAAS,iBAAiB,SAAS,QAAQ,OAAO,OAAO,cAAc;AACrE,MAAI,OAAO;AACT,WAAO;AAAA,MACL;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,YAAY;AAChB,MAAI;AACJ,MAAI;AACF,gBAAY,QAAQ,OAAO,MAAM;AAAA,EACnC,SAAS,KAAK;AACZ,gBAAY,IAAI,SAAS;AACzB,iBAAa,YAAY,GAAG;AAAA,EAC9B;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,OAAO;AAAA,EACT;AACF;AAIA,SAAS,gBAAgB,gBAAgB,0BAA0B,SAAS,gBAAgB,aAAa,iBAAiB,kBAAkB,cAAc,UAAU;AAGlK,MAAI,4BAA4B,eAAe,UAAU,eAAe,WAAW,gBAAgB,QAAQ;AACzG,WAAO;AAAA,EACT;AACA,QAAM,qBAAqB,eAAe,MAAM,GAAG,wBAAwB;AAG3E,QAAM,uBAAuB,gBAAgB,UAAU,WAAW,IAAI;AACtE,WAAS,IAAI,0BAA0B,IAAI,sBAAsB,KAAK;AACpE,UAAM,WAAW,gBAAgB,CAAC;AAClC,UAAM,SAAS,YAAY,QAAQ,EAAE;AACrC,UAAM,gBAAgB,mBAAmB,IAAI,CAAC;AAC9C,UAAM,gBAAgB,gBAAgB,cAAc,QAAQ;AAC5D,UAAM,gBAAgB,gBAAgB,cAAc,QAAQ;AAC5D,UAAM,aAAa,iBAAiB,QAAQ,QAAQ,IAAI;AACxD,UAAM,QAAQ,aAAa,gBAAgB,iBAAiB,SAAS,QAAQ,eAAe,eAAe,YAAY;AACvH,uBAAmB,KAAK,KAAK;AAAA,EAC/B;AAGA,MAAI,UAAU;AACZ,uBAAmB,KAAK,eAAe,eAAe,SAAS,CAAC,CAAC;AAAA,EACnE;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,uBAAuB,gBAAgB;AAC/D,SAAO;AAAA,IACL,cAAc,eAAe,QAAW,CAAC,CAAC;AAAA,IAC1C,cAAc;AAAA,IACd,aAAa;AAAA,MACX,GAAG,WAAW,WAAW;AAAA,IAC3B;AAAA,IACA,iBAAiB,CAAC,CAAC;AAAA,IACnB,kBAAkB,CAAC;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,gBAAgB,CAAC;AAAA,IACjB,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AACF;AAIA,SAAS,gBAAgB,uBAAuB,oBAAoB,cAAc,gBAAgB,UAAU,CAAC,GAAG;AAI9G,SAAO,aAAW,CAAC,aAAa,iBAAiB;AAC/C,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,eAAe;AACnB,QAAI,CAAC,aAAa;AAEhB,oBAAc,OAAO,OAAO,WAAW;AAAA,IACzC;AACA,aAAS,oBAAoB,GAAG;AAE9B,UAAI,SAAS;AACb,UAAI,cAAc,gBAAgB,MAAM,GAAG,SAAS,CAAC;AACrD,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAI,eAAe,IAAI,CAAC,EAAE,OAAO;AAE/B,mBAAS;AACT,wBAAc,gBAAgB,MAAM,GAAG,SAAS,CAAC;AACjD;AAAA,QACF,OAAO;AACL,iBAAO,YAAY,YAAY,CAAC,CAAC;AAAA,QACnC;AAAA,MACF;AACA,yBAAmB,iBAAiB,OAAO,QAAM,YAAY,QAAQ,EAAE,MAAM,EAAE;AAC/E,wBAAkB,CAAC,GAAG,GAAG,gBAAgB,MAAM,SAAS,CAAC,CAAC;AAC1D,uBAAiB,eAAe,MAAM,EAAE;AACxC,uBAAiB,eAAe,MAAM,MAAM;AAC5C,0BAAoB,oBAAoB,SAAS,oBAAoB,SAAS;AAAA,IAChF;AACA,aAAS,gBAAgB;AAGvB,oBAAc;AAAA,QACZ,GAAG,WAAW,WAAW;AAAA,MAC3B;AACA,qBAAe;AACf,wBAAkB,CAAC,CAAC;AACpB,yBAAmB,CAAC;AACpB,uBAAiB,eAAe,iBAAiB,EAAE;AACnD,0BAAoB;AACpB,uBAAiB,CAAC;AAAA,IACpB;AAIA,QAAI,2BAA2B;AAC/B,YAAQ,aAAa,MAAM;AAAA,MACzB,KAAK,cACH;AACE,mBAAW,aAAa;AACxB,mCAA2B;AAC3B;AAAA,MACF;AAAA,MACF,KAAK,iBACH;AACE,mBAAW,aAAa;AACxB,YAAI,UAAU;AAIZ,4BAAkB,CAAC,GAAG,iBAAiB,YAAY;AACnD,sBAAY,YAAY,IAAI,IAAI,cAAc;AAAA,YAC5C,MAAM;AAAA,UACR,GAAG,CAAC,KAAK,IAAI,CAAC;AACd;AACA,qCAA2B,gBAAgB,SAAS;AACpD,2BAAiB,eAAe,OAAO,eAAe,eAAe,SAAS,CAAC,CAAC;AAChF,cAAI,sBAAsB,gBAAgB,SAAS,GAAG;AACpD;AAAA,UACF;AACA,qCAA2B;AAAA,QAC7B,OAAO;AACL,wBAAc;AAAA,QAChB;AACA;AAAA,MACF;AAAA,MACF,KAAK,OACH;AAEE,sBAAc;AAAA,UACZ,GAAG,WAAW,WAAW;AAAA,QAC3B;AACA,uBAAe;AACf,0BAAkB,CAAC,CAAC;AACpB,2BAAmB,CAAC;AACpB,yBAAiB;AACjB,4BAAoB;AACpB,yBAAiB,CAAC;AAClB;AAAA,MACF;AAAA,MACF,KAAK,QACH;AACE,sBAAc;AACd;AAAA,MACF;AAAA,MACF,KAAK,UACH;AAGE,sBAAc;AAAA,UACZ,GAAG,WAAW,WAAW;AAAA,QAC3B;AACA,uBAAe;AACf,0BAAkB,CAAC,CAAC;AACpB,2BAAmB,CAAC;AACpB,4BAAoB;AACpB,yBAAiB,CAAC;AAClB;AAAA,MACF;AAAA,MACF,KAAK,eACH;AAGE,cAAM;AAAA,UACJ,IAAI;AAAA,QACN,IAAI;AACJ,cAAM,QAAQ,iBAAiB,QAAQ,QAAQ;AAC/C,YAAI,UAAU,IAAI;AAChB,6BAAmB,CAAC,UAAU,GAAG,gBAAgB;AAAA,QACnD,OAAO;AACL,6BAAmB,iBAAiB,OAAO,QAAM,OAAO,QAAQ;AAAA,QAClE;AAEA,mCAA2B,gBAAgB,QAAQ,QAAQ;AAC3D;AAAA,MACF;AAAA,MACF,KAAK,oBACH;AAGE,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,YAAY,CAAC;AACnB,iBAAS,IAAI,OAAO,IAAI,KAAK,IAAK,WAAU,KAAK,CAAC;AAClD,YAAI,QAAQ;AACV,6BAAmB,WAAW,kBAAkB,SAAS;AAAA,QAC3D,OAAO;AACL,6BAAmB,CAAC,GAAG,kBAAkB,GAAG,SAAS;AAAA,QACvD;AAEA,mCAA2B,gBAAgB,QAAQ,KAAK;AACxD;AAAA,MACF;AAAA,MACF,KAAK,eACH;AAGE,4BAAoB,aAAa;AAEjC,mCAA2B;AAC3B;AAAA,MACF;AAAA,MACF,KAAK,gBACH;AAGE,cAAM,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ;AAC3D,YAAI,UAAU,GAAI,qBAAoB;AACtC,mCAA2B;AAC3B;AAAA,MACF;AAAA,MACF,KAAK,OACH;AAEE,0BAAkB,WAAW,iBAAiB,gBAAgB;AAC9D,2BAAmB,CAAC;AACpB,4BAAoB,KAAK,IAAI,mBAAmB,gBAAgB,SAAS,CAAC;AAC1E;AAAA,MACF;AAAA,MACF,KAAK,gBACH;AAEE,YAAI,UAAU;AACZ,iBAAO,eAAe;AAAA,QACxB;AACA,YAAI,YAAY,eAAe,iBAAiB,YAAY,eAAe,iBAAiB,GAAG,cAAc,QAAQ,WAAW,QAAQ,iBAAiB,QAAQ,gBAAgB,GAAG;AAKlL,gBAAM,YAAY,eAAe,eAAe,SAAS,CAAC;AAC1D,2BAAiB,CAAC,GAAG,eAAe,MAAM,GAAG,EAAE,GAAG,iBAAiB,SAAS,aAAa,QAAQ,UAAU,OAAO,UAAU,OAAO,YAAY,CAAC;AAChJ,qCAA2B;AAC3B;AAAA,QACF;AAEA,YAAI,QAAQ,UAAU,gBAAgB,WAAW,QAAQ,QAAQ;AAC/D,8BAAoB,CAAC;AAAA,QACvB;AACA,YAAI,sBAAsB,gBAAgB,SAAS,GAAG;AACpD;AAAA,QACF;AACA,cAAM,WAAW;AAGjB,oBAAY,QAAQ,IAAI;AACxB,0BAAkB,CAAC,GAAG,iBAAiB,QAAQ;AAE/C,mCAA2B,gBAAgB,SAAS;AACpD;AAAA,MACF;AAAA,MACF,KAAK,cACH;AAEE,SAAC;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,aAAa;AACjB;AAAA,MACF;AAAA,MACF,KAAK,MACH;AAEE,mCAA2B;AAC3B,YAAI,QAAQ,UAAU,gBAAgB,SAAS,QAAQ,QAAQ;AAE7D,2BAAiB,gBAAgB,gBAAgB,0BAA0B,SAAS,gBAAgB,aAAa,iBAAiB,kBAAkB,cAAc,QAAQ;AAC1K,8BAAoB,gBAAgB,SAAS,QAAQ,MAAM;AAE3D,qCAA2B;AAAA,QAC7B;AACA;AAAA,MACF;AAAA,MACF,KAAK,QACH;AACE,cAAM,iBAAiB,eAAe,OAAO,WAAS,MAAM,KAAK,EAAE,SAAS;AAC5E,YAAI,gBAAgB;AAElB,qCAA2B;AAC3B,cAAI,QAAQ,UAAU,gBAAgB,SAAS,QAAQ,QAAQ;AAE7D,6BAAiB,gBAAgB,gBAAgB,0BAA0B,SAAS,gBAAgB,aAAa,iBAAiB,kBAAkB,cAAc,QAAQ;AAC1K,gCAAoB,gBAAgB,SAAS,QAAQ,MAAM;AAE3D,uCAA2B;AAAA,UAC7B;AAAA,QACF,OAAO;AAGL,cAAI,CAAC,YAAY,CAAC,UAAU;AAC1B,gBAAI,sBAAsB,gBAAgB,SAAS,GAAG;AACpD;AAAA,YACF;AAEA,kBAAM,WAAW;AACjB,wBAAY,QAAQ,IAAI,IAAI,cAAc,cAAc,CAAC,KAAK,IAAI,CAAC;AACnE,8BAAkB,CAAC,GAAG,iBAAiB,QAAQ;AAC/C,uCAA2B,gBAAgB,SAAS;AACpD,6BAAiB,gBAAgB,gBAAgB,0BAA0B,SAAS,gBAAgB,aAAa,iBAAiB,kBAAkB,cAAc,QAAQ;AAAA,UAC5K;AAEA,2BAAiB,eAAe,IAAI,SAAQ,iCACvC,MADuC;AAAA,YAE1C,OAAO,QAAQ,IAAI,OAAO,gBAAgB;AAAA,UAC5C,EAAE;AACF,8BAAoB,gBAAgB,SAAS;AAC7C,cAAI,QAAQ,UAAU,gBAAgB,SAAS,QAAQ,QAAQ;AAC7D,gCAAoB,gBAAgB,SAAS,QAAQ,MAAM;AAAA,UAC7D;AAEA,qCAA2B;AAAA,QAC7B;AACA;AAAA,MACF;AAAA,MACF,SACE;AAGE,mCAA2B;AAC3B;AAAA,MACF;AAAA,IACJ;AACA,qBAAiB,gBAAgB,gBAAgB,0BAA0B,SAAS,gBAAgB,aAAa,iBAAiB,kBAAkB,cAAc,QAAQ;AAC1K,mBAAe,eAAe,cAAc,YAAY;AACxD,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,YAAY,UAAU,WAAW,WAAW,gBAAgB,cAAc,cAAc,QAAQ;AAC1G,UAAM,qBAAqB,iBAAiB,cAAc,OAAO,OAAO;AACxE,UAAM,cAAc,gBAAgB,cAAc,oBAAoB,cAAc,OAAO,SAAS,MAAM;AAC1G,UAAM,gBAAgB,MAAM,MAAM,SAAS,aAAa,EAAE,KAAK,KAAK,CAAC,CAAC,GAAG,UAAU,QAAQ,EAAE,KAAK,IAAI,UAAU,CAAC,GAAG,YAAY,UAAU,cAAc,EAAE,KAAK,UAAU,cAAc,CAAC;AACxL,UAAM,iBAAiB,UAAU,KAAK,IAAI,WAAW,CAAC;AACtD,UAAM,aAAa,iBAAiB,OAAO,aAAa;AACxD,UAAM,qBAAqB,IAAI,cAAc,CAAC;AAC9C,SAAK,0BAA0B,cAAc;AAAA,MAAK,eAAe,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAO/E,WAAW,UAAU;AAAA,MAAG,KAAK,CAAC;AAAA,QAC5B,OAAO;AAAA,MACT,GAAG,CAAC,QAAQ,OAAO,MAAM;AACvB,YAAI,qBAAqB,QAAQ,aAAa,MAAM;AAGpD,YAAI,OAAO,SAAS,kBAAkB,oBAAoB,MAAM,GAAG;AACjE,+BAAqB,kBAAkB,oBAAoB,OAAO,WAAW,OAAO,iBAAiB,OAAO,gBAAgB;AAAA,QAC9H;AAEA,kBAAU,OAAO,QAAQ,kBAAkB;AAC3C,eAAO;AAAA,UACL,OAAO;AAAA,UACP;AAAA,QACF;AAAA,MACF,GAAG;AAAA,QACD,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AAAA,IAAC,EAAE,UAAU,CAAC;AAAA,MACb;AAAA,MACA;AAAA,IACF,MAAM;AACJ,yBAAmB,KAAK,KAAK;AAC7B,UAAI,OAAO,SAAS,gBAAgB;AAClC,cAAM,iBAAiB,OAAO;AAC9B,uBAAe,KAAK,cAAc;AAAA,MACpC;AAAA,IACF,CAAC;AACD,SAAK,6BAA6B,UAAU,OAAO,KAAK,WAAW,UAAU,CAAC,EAAE,UAAU,MAAM;AAC9F,WAAK,QAAQ;AAAA,IACf,CAAC;AACD,UAAM,eAAe,mBAAmB,aAAa;AACrD,UAAM,SAAS,aAAa,KAAK,IAAI,WAAW,CAAC;AACjD,WAAO,eAAe,QAAQ,SAAS;AAAA,MACrC,OAAO,SAAS,QAAQ;AAAA,QACtB,eAAe;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AACD,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,cAAc;AAMZ,SAAK,wBAAwB,YAAY;AACzC,SAAK,2BAA2B,YAAY;AAAA,EAC9C;AAAA,EACA,SAAS,QAAQ;AACf,SAAK,WAAW,KAAK,MAAM;AAAA,EAC7B;AAAA,EACA,KAAK,QAAQ;AACX,SAAK,WAAW,KAAK,MAAM;AAAA,EAC7B;AAAA,EACA,MAAM,OAAO;AAAA,EAAC;AAAA,EACd,WAAW;AAAA,EAAC;AAAA,EACZ,cAAc,QAAQ;AACpB,SAAK,SAAS,IAAI,cAAc,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC;AAAA,EACtD;AAAA,EACA,UAAU;AACR,SAAK,SAAS,IAAI,QAAQ,CAAC;AAAA,EAC7B;AAAA,EACA,QAAQ;AACN,SAAK,SAAS,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;AAAA,EACtC;AAAA,EACA,WAAW;AACT,SAAK,SAAS,IAAI,SAAS,CAAC,KAAK,IAAI,CAAC,CAAC;AAAA,EACzC;AAAA,EACA,SAAS;AACP,SAAK,SAAS,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC;AAAA,EACvC;AAAA,EACA,QAAQ;AACN,SAAK,SAAS,IAAI,MAAM,CAAC;AAAA,EAC3B;AAAA,EACA,aAAa,IAAI;AACf,SAAK,SAAS,IAAI,aAAa,EAAE,CAAC;AAAA,EACpC;AAAA,EACA,aAAa,UAAU;AACrB,SAAK,SAAS,IAAI,aAAa,QAAQ,CAAC;AAAA,EAC1C;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,SAAS,IAAI,YAAY,KAAK,CAAC;AAAA,EACtC;AAAA,EACA,YAAY,iBAAiB;AAC3B,SAAK,SAAS,IAAI,YAAY,eAAe,CAAC;AAAA,EAChD;AAAA,EACA,YAAY,QAAQ;AAClB,SAAK,SAAS,IAAI,YAAY,MAAM,CAAC;AAAA,EACvC;AAAA,EACA,eAAe,QAAQ;AACrB,SAAK,SAAS,IAAI,eAAe,MAAM,CAAC;AAAA,EAC1C;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAkB,SAAS,kBAAkB,GAAM,SAAY,cAAc,GAAM,SAAY,iBAAiB,GAAM,SAAS,iBAAiB,GAAM,SAAY,qBAAqB,GAAM,SAAY,YAAY,GAAM,SAAS,aAAa,GAAM,SAAS,qBAAqB,CAAC;AAAA,IACzS;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,SAAS,WAAW;AAAA,EAClB;AAAA,EACA;AACF,GAAG;AACD,SAAO,YAAU,gBAAgB,IAAI,WAAW,gBAAc,OAAO,UAAU;AAAA,IAC7E,MAAM,WAAS,OAAO,IAAI,MAAM,WAAW,KAAK,KAAK,CAAC;AAAA,IACtD,OAAO,WAAS,OAAO,IAAI,MAAM,WAAW,MAAM,KAAK,CAAC;AAAA,IACxD,UAAU,MAAM,OAAO,IAAI,MAAM,WAAW,SAAS,CAAC;AAAA,EACxD,CAAC,CAAC,IAAI;AACR;AACA,IAAM,kCAAkC,IAAI,eAAe,+DAA+D;AAC1H,SAAS,kCAAkC,WAAW,QAAQ;AAC5D,SAAO,QAAQ,SAAS,KAAK,OAAO,YAAY;AAClD;AACA,SAAS,+BAA+B;AACtC,QAAM,eAAe;AACrB,MAAI,OAAO,WAAW,YAAY,OAAO,OAAO,YAAY,MAAM,aAAa;AAC7E,WAAO,OAAO,YAAY;AAAA,EAC5B,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAiBA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AAC1C,SAAO,yBAAyB,CAAC,mBAAmB,oBAAoB,eAAe;AAAA,IACrF,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,MAAM,CAAC,0BAA0B,qBAAqB;AAAA,IACtD,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,MAAM,CAAC,eAAe;AAAA,IACtB,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,MAAM,CAAC,aAAa;AAAA,IACpB,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC;AACJ;AACA,SAAS,sBAAsB,UAAU;AACvC,SAAO,SAAS;AAClB;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,WAAW,UAAU,CAAC,GAAG;AAC9B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB,OAAO,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["state", "action"]}