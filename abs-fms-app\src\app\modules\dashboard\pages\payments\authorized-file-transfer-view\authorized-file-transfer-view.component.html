<div class="container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="title-section">
      <button mat-icon-button (click)="goBack()" class="back-button" matTooltip="Go Back">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <div class="title-content">
        <h2>Payment Details</h2>
        <p *ngIf="transfer">{{ transfer.voucherNoRef }} - {{ getTypeLabel(transfer.type) }}</p>
      </div>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading payment details...</p>
  </div>

  <!-- Action Buttons Section -->
      <div class="action-buttons-section">
        <div class="action-buttons-container">
          <!-- Change Bank Account Button -->
          <div class="bank-account-section">
            <app-bank-account-selector
              label="Select Bank Account"
              placeholder="Choose bank account for payment"
              [required]="true"
              [showBalance]="false"
              [showAccountNumber]="true"
              [showBankName]="true"
              filterByStatus="Active"
              (accountSelected)="onBankAccountSelected($event)"
              (accountCleared)="onBankAccountCleared()"
              class="bank-account-field">
            </app-bank-account-selector>
          </div>

          <!-- Authorize Button -->
          <button mat-raised-button color="accent" (click)="authorizePayment()"
                  [disabled]="!selectedBankAccount" class="authorize-btn">
            <mat-icon>verified</mat-icon>
            Authorize Payment
          </button>

          <!-- Go to Update EFTs Button -->
          <!-- <button mat-raised-button color="warn" (click)="goToUpdateEFTs()" class="update-efts-btn">
            <mat-icon>update</mat-icon>
            Go to Update EFTs
          </button> -->
        </div>
      </div>


  <!-- Transfer Details -->
  <div *ngIf="!isLoading && transfer" class="details-section">
    <mat-card class="details-card">
      <mat-card-header>
        <mat-card-title>
          <div class="card-title-content">
            <span class="card-title-space">{{ transfer.voucherNoRef }}</span>
            <div class="badges">
              <span class="type-badge type-{{ transfer.type.toLowerCase() }}">
                {{ getTypeLabel(transfer.type) }}
              </span>
            </div>
          </div>
        </mat-card-title>
      </mat-card-header>

      
      <mat-card-content>
        <mat-tab-group class="details-tabs" animationDuration="300ms">
          <!-- Basic Information Tab -->
          <mat-tab label="Basic Information">
            <div class="tab-content">
              <div class="detail-row">
                <label>Voucher No/Ref:</label>
                <span class="detail-value">{{ transfer.voucherNoRef }}</span>
              </div>
              <div class="detail-row">
                <label>Date:</label>
                <span class="detail-value">{{ transfer.date | date:'dd/MM/yyyy' }}</span>
              </div>
              <div class="detail-row">
                <label>Type:</label>
                <span class="type-badge type-{{ transfer.type.toLowerCase() }}">
                  {{ getTypeLabel(transfer.type) }}
                </span>
              </div>
            </div>
          </mat-tab>

          <!-- Financial Information Tab -->
          <mat-tab label="Financial Information">
            <div class="tab-content">
              <div class="detail-row">
                <label>Payee:</label>
                <span class="detail-value">{{ transfer.payee }}</span>
              </div>
              <div class="detail-row">
                <label>Amount:</label>
                <span class="detail-value amount-highlight">
                  {{ formatCurrency(transfer.amountPayee, transfer.currencyCode) }}
                </span>
              </div>
              <div class="detail-row">
                <label>Currency Code:</label>
                <span class="detail-value">{{ transfer.currencyCode }}</span>
              </div>
              <div class="detail-row">
                <label>Bank Account:</label>
                <span class="detail-value">{{ transfer.bankAccount }}</span>
              </div>
            </div>
          </mat-tab>

          <!-- Authorization Information Tab -->
          <mat-tab label="Authorization Information">
            <div class="tab-content">
              <div class="detail-row">
                <label>Authorized By:</label>
                <span class="detail-value">{{ transfer.authorisedBy }}</span>
              </div>
              <div class="detail-row">
                <label>Authorization Date:</label>
                <span class="detail-value">{{ transfer.authorizationDate | date:'dd/MM/yyyy' }}</span>
              </div>
              <div class="detail-row">
                <label>Prepared By:</label>
                <span class="detail-value">{{ transfer.preparedBy }}</span>
              </div>
              <div class="detail-row">
                <label>Request Date:</label>
                <span class="detail-value">{{ transfer.requestDate | date:'dd/MM/yyyy' }}</span>
              </div>
            </div>
          </mat-tab>

          <!-- Document Information Tab -->
          <mat-tab label="Document Information">
            <div class="tab-content">
              <div class="detail-row">
                <label>Document:</label>
                <span class="detail-value document-link">{{ transfer.document }}</span>
              </div>
              <div class="detail-row">
                <label>Remarks:</label>
                <span class="detail-value description-text">{{ transfer.remarks }}</span>
              </div>
              <div class="detail-row">
                <label>Narrative:</label>
                <span class="detail-value description-text">{{ transfer.narrative }}</span>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>

      <!-- <mat-card-actions class="card-actions">
        <button mat-raised-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
          Back to Search
        </button>
      </mat-card-actions> -->
    </mat-card>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !transfer" class="error-section">
    <mat-card class="error-card">
      <mat-card-content>
        <div class="error-content">
          <mat-icon class="error-icon">error_outline</mat-icon>
          <h3>Transfer Not Found</h3>
          <p>The requested transfer could not be found or may have been removed.</p>
          <button mat-raised-button color="primary" (click)="goBack()">
            <mat-icon>arrow_back</mat-icon>
            Back to Search
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<!-- Authorization Confirmation Modal -->
<div class="modal-overlay" *ngIf="showAuthorizationModal" (click)="closeAuthorizationModal()">
  <div class="modal-content authorization-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>
        <mat-icon class="modal-icon">verified</mat-icon>
        Authorize Payment
      </h3>
      <button mat-icon-button (click)="closeAuthorizationModal()" class="close-button">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div class="modal-body" *ngIf="authorizationData">
      <div class="authorization-details">
        <!-- <div class="warning-section">
          <mat-icon class="warning-icon">warning</mat-icon>
          <p>You are about to authorize a payment. Please review the details carefully before proceeding.</p>
        </div> -->

        <div class="payment-summary">
          <h4>Payment Summary</h4>
          <div class="summary-grid">
            <div class="summary-row">
              <label>Voucher Reference:</label>
              <span class="value">{{ authorizationData.transfer.voucherNoRef }}</span>
            </div>
            <div class="summary-row">
              <label>Payee:</label>
              <span class="value payee-highlight">{{ authorizationData.transfer.payee }}</span>
            </div>
            <div class="summary-row">
              <label>Amount:</label>
              <span class="value amount-highlight">{{ authorizationData.amount }}</span>
            </div>
            <div class="summary-row">
              <label>Payee Bank Account:</label>
              <span class="value">{{ authorizationData.transfer.bankAccount }}</span>
            </div>
            <div class="summary-row">
              <label>From Account:</label>
              <span class="value">{{ authorizationData.selectedAccount.bankName }} - {{ authorizationData.selectedAccount.accountNumber }}</span>
            </div>
            <div class="summary-row">
              <label>Transfer Type:</label>
              <span class="value">
                <span class="type-badge type-{{ authorizationData.transfer.type.toLowerCase() }}">
                  {{ getTypeLabel(authorizationData.transfer.type) }}
                </span>
              </span>
            </div>
            <div class="summary-row">
              <label>Narrative:</label>
              <span class="value">{{ authorizationData.transfer.narrative }}</span>
            </div>
          </div>
        </div>

        <div class="confirmation-section">
          <mat-icon class="check-icon">check_circle</mat-icon>
          <p>By clicking "Authorize Payment", you confirm that you have reviewed all details and authorize this payment to be processed.</p>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button mat-button (click)="closeAuthorizationModal()" class="cancel-button">
        <mat-icon>cancel</mat-icon>
        Cancel
      </button>
      <button mat-raised-button color="primary" (click)="confirmAuthorization()" class="authorize-button">
        <mat-icon>verified</mat-icon>
        Authorize Payment
      </button>
    </div>
  </div>
</div>
