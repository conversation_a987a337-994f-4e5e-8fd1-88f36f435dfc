import {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  MatAnchor,
  MatButton,
  MatButtonModule,
  MatFabAnchor,
  MatFabButton,
  MatIconAnchor,
  <PERSON><PERSON><PERSON>Button,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-5EJZ4M5W.js";
import "./chunk-UTYB2JNB.js";
import "./chunk-APQJ6POP.js";
import "./chunk-IGJZNA3K.js";
import "./chunk-V4GYEGQC.js";
import "./chunk-CONQKHOI.js";
import "./chunk-GC5FLHL6.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON>nch<PERSON>,
  <PERSON><PERSON>utt<PERSON>,
  MatButtonModule,
  Mat<PERSON><PERSON><PERSON>nch<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>on,
  <PERSON><PERSON><PERSON>Anch<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>iniFabAnchor,
  MatMiniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map
