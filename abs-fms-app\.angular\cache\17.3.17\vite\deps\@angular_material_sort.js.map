{"version": 3, "sources": ["../../../../../node_modules/@angular/material/fesm2022/sort.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, booleanAttribute, Directive, Optional, Inject, Input, Output, Injectable, SkipSelf, Component, ViewEncapsulation, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i3 from '@angular/cdk/a11y';\nimport { SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { ReplaySubject, Subject, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes, query, animateChild } from '@angular/animations';\nimport { AnimationDurations, AnimationCurves, MatCommonModule } from '@angular/material/core';\n\n/** @docs-private */\nconst _c0 = [\"mat-sort-header\", \"\"];\nconst _c1 = [\"*\"];\nfunction MatSortHeader_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"@arrowPosition.start\", function MatSortHeader_Conditional_3_Template_div_animation_arrowPosition_start_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._disableViewStateAnimation = true);\n    })(\"@arrowPosition.done\", function MatSortHeader_Conditional_3_Template_div_animation_arrowPosition_done_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._disableViewStateAnimation = false);\n    });\n    i0.ɵɵelement(1, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4);\n    i0.ɵɵelement(3, \"div\", 5)(4, \"div\", 6)(5, \"div\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@arrowOpacity\", ctx_r1._getArrowViewState())(\"@arrowPosition\", ctx_r1._getArrowViewState())(\"@allowChildren\", ctx_r1._getArrowDirectionState());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"@indicator\", ctx_r1._getArrowDirectionState());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@leftPointer\", ctx_r1._getArrowDirectionState());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@rightPointer\", ctx_r1._getArrowDirectionState());\n  }\n}\nfunction getSortDuplicateSortableIdError(id) {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n/** @docs-private */\nfunction getSortHeaderNotContainedWithinSortError() {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n/** @docs-private */\nfunction getSortHeaderMissingIdError() {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n/** @docs-private */\nfunction getSortInvalidDirectionError(direction) {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nconst MAT_SORT_DEFAULT_OPTIONS = new InjectionToken('MAT_SORT_DEFAULT_OPTIONS');\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\nclass MatSort {\n  /** The sort direction of the currently active MatSortable. */\n  get direction() {\n    return this._direction;\n  }\n  set direction(direction) {\n    if (direction && direction !== 'asc' && direction !== 'desc' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortInvalidDirectionError(direction);\n    }\n    this._direction = direction;\n  }\n  constructor(_defaultOptions) {\n    this._defaultOptions = _defaultOptions;\n    this._initializedStream = new ReplaySubject(1);\n    /** Collection of all registered sortables that this directive manages. */\n    this.sortables = new Map();\n    /** Used to notify any child components listening to state changes. */\n    this._stateChanges = new Subject();\n    /**\n     * The direction to set when an MatSortable is initially sorted.\n     * May be overridden by the MatSortable's sort start.\n     */\n    this.start = 'asc';\n    this._direction = '';\n    /** Whether the sortable is disabled. */\n    this.disabled = false;\n    /** Event emitted when the user changes either the active sort or sort direction. */\n    this.sortChange = new EventEmitter();\n    /** Emits when the paginator is initialized. */\n    this.initialized = this._initializedStream;\n  }\n  /**\n   * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n   * collection of MatSortables.\n   */\n  register(sortable) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!sortable.id) {\n        throw getSortHeaderMissingIdError();\n      }\n      if (this.sortables.has(sortable.id)) {\n        throw getSortDuplicateSortableIdError(sortable.id);\n      }\n    }\n    this.sortables.set(sortable.id, sortable);\n  }\n  /**\n   * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n   * collection of contained MatSortables.\n   */\n  deregister(sortable) {\n    this.sortables.delete(sortable.id);\n  }\n  /** Sets the active sort id and determines the new sort direction. */\n  sort(sortable) {\n    if (this.active != sortable.id) {\n      this.active = sortable.id;\n      this.direction = sortable.start ? sortable.start : this.start;\n    } else {\n      this.direction = this.getNextSortDirection(sortable);\n    }\n    this.sortChange.emit({\n      active: this.active,\n      direction: this.direction\n    });\n  }\n  /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n  getNextSortDirection(sortable) {\n    if (!sortable) {\n      return '';\n    }\n    // Get the sort direction cycle with the potential sortable overrides.\n    const disableClear = sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n    let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n    // Get and return the next direction in the cycle\n    let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n    if (nextDirectionIndex >= sortDirectionCycle.length) {\n      nextDirectionIndex = 0;\n    }\n    return sortDirectionCycle[nextDirectionIndex];\n  }\n  ngOnInit() {\n    this._initializedStream.next();\n  }\n  ngOnChanges() {\n    this._stateChanges.next();\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._initializedStream.complete();\n  }\n  static {\n    this.ɵfac = function MatSort_Factory(t) {\n      return new (t || MatSort)(i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSort,\n      selectors: [[\"\", \"matSort\", \"\"]],\n      hostAttrs: [1, \"mat-sort\"],\n      inputs: {\n        active: [i0.ɵɵInputFlags.None, \"matSortActive\", \"active\"],\n        start: [i0.ɵɵInputFlags.None, \"matSortStart\", \"start\"],\n        direction: [i0.ɵɵInputFlags.None, \"matSortDirection\", \"direction\"],\n        disableClear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matSortDisableClear\", \"disableClear\", booleanAttribute],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matSortDisabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        sortChange: \"matSortChange\"\n      },\n      exportAs: [\"matSort\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSort, [{\n    type: Directive,\n    args: [{\n      selector: '[matSort]',\n      exportAs: 'matSort',\n      host: {\n        'class': 'mat-sort'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SORT_DEFAULT_OPTIONS]\n    }]\n  }], {\n    active: [{\n      type: Input,\n      args: ['matSortActive']\n    }],\n    start: [{\n      type: Input,\n      args: ['matSortStart']\n    }],\n    direction: [{\n      type: Input,\n      args: ['matSortDirection']\n    }],\n    disableClear: [{\n      type: Input,\n      args: [{\n        alias: 'matSortDisableClear',\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'matSortDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    sortChange: [{\n      type: Output,\n      args: ['matSortChange']\n    }]\n  });\n})();\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start, disableClear) {\n  let sortOrder = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n  return sortOrder;\n}\nconst SORT_ANIMATION_TRANSITION = AnimationDurations.ENTERING + ' ' + AnimationCurves.STANDARD_CURVE;\n/**\n * Animations used by MatSort.\n * @docs-private\n */\nconst matSortAnimations = {\n  /** Animation that moves the sort indicator. */\n  indicator: trigger('indicator', [state('active-asc, asc', style({\n    transform: 'translateY(0px)'\n  })),\n  // 10px is the height of the sort indicator, minus the width of the pointers\n  state('active-desc, desc', style({\n    transform: 'translateY(10px)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: trigger('leftPointer', [state('active-asc, asc', style({\n    transform: 'rotate(-45deg)'\n  })), state('active-desc, desc', style({\n    transform: 'rotate(45deg)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: trigger('rightPointer', [state('active-asc, asc', style({\n    transform: 'rotate(45deg)'\n  })), state('active-desc, desc', style({\n    transform: 'rotate(-45deg)'\n  })), transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION))]),\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: trigger('arrowOpacity', [state('desc-to-active, asc-to-active, active', style({\n    opacity: 1\n  })), state('desc-to-hint, asc-to-hint, hint', style({\n    opacity: 0.54\n  })), state('hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void', style({\n    opacity: 0\n  })),\n  // Transition between all states except for immediate transitions\n  transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')), transition('* <=> *', animate(SORT_ANIMATION_TRANSITION))]),\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: trigger('arrowPosition', [\n  // Hidden Above => Hint Center\n  transition('* => desc-to-hint, * => desc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(-25%)'\n  }), style({\n    transform: 'translateY(0)'\n  })]))),\n  // Hint Center => Hidden Below\n  transition('* => hint-to-desc, * => active-to-desc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(0)'\n  }), style({\n    transform: 'translateY(25%)'\n  })]))),\n  // Hidden Below => Hint Center\n  transition('* => asc-to-hint, * => asc-to-active', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(25%)'\n  }), style({\n    transform: 'translateY(0)'\n  })]))),\n  // Hint Center => Hidden Above\n  transition('* => hint-to-asc, * => active-to-asc', animate(SORT_ANIMATION_TRANSITION, keyframes([style({\n    transform: 'translateY(0)'\n  }), style({\n    transform: 'translateY(-25%)'\n  })]))), state('desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active', style({\n    transform: 'translateY(0)'\n  })), state('hint-to-desc, active-to-desc, desc', style({\n    transform: 'translateY(-25%)'\n  })), state('hint-to-asc, active-to-asc, asc', style({\n    transform: 'translateY(25%)'\n  }))]),\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: trigger('allowChildren', [transition('* <=> *', [query('@*', animateChild(), {\n    optional: true\n  })])])\n};\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\nclass MatSortHeaderIntl {\n  constructor() {\n    /**\n     * Stream that emits whenever the labels here are changed. Use this to notify\n     * components if the labels have changed after initialization.\n     */\n    this.changes = new Subject();\n  }\n  static {\n    this.ɵfac = function MatSortHeaderIntl_Factory(t) {\n      return new (t || MatSortHeaderIntl)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatSortHeaderIntl,\n      factory: MatSortHeaderIntl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeaderIntl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** @docs-private */\nfunction MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n/** @docs-private */\nconst MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY\n};\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\nclass MatSortHeader {\n  /**\n   * Description applied to MatSortHeader's button element with aria-describedby. This text should\n   * describe the action that will occur when the user clicks the sort header.\n   */\n  get sortActionDescription() {\n    return this._sortActionDescription;\n  }\n  set sortActionDescription(value) {\n    this._updateSortActionDescription(value);\n  }\n  constructor(\n  /**\n   * @deprecated `_intl` parameter isn't being used anymore and it'll be removed.\n   * @breaking-change 13.0.0\n   */\n  _intl, _changeDetectorRef,\n  // `MatSort` is not optionally injected, but just asserted manually w/ better error.\n  // tslint:disable-next-line: lightweight-tokens\n  _sort, _columnDef, _focusMonitor, _elementRef, /** @breaking-change 14.0.0 _ariaDescriber will be required. */\n  _ariaDescriber, defaultOptions) {\n    this._intl = _intl;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._sort = _sort;\n    this._columnDef = _columnDef;\n    this._focusMonitor = _focusMonitor;\n    this._elementRef = _elementRef;\n    this._ariaDescriber = _ariaDescriber;\n    /**\n     * Flag set to true when the indicator should be displayed while the sort is not active. Used to\n     * provide an affordance that the header is sortable by showing on focus and hover.\n     */\n    this._showIndicatorHint = false;\n    /**\n     * The view transition state of the arrow (translation/ opacity) - indicates its `from` and `to`\n     * position through the animation. If animations are currently disabled, the fromState is removed\n     * so that there is no animation displayed.\n     */\n    this._viewState = {};\n    /** The direction the arrow should be facing according to the current state. */\n    this._arrowDirection = '';\n    /**\n     * Whether the view state animation should show the transition between the `from` and `to` states.\n     */\n    this._disableViewStateAnimation = false;\n    /** Sets the position of the arrow that displays when sorted. */\n    this.arrowPosition = 'after';\n    /** whether the sort header is disabled. */\n    this.disabled = false;\n    // Default the action description to \"Sort\" because it's better than nothing.\n    // Without a description, the button's label comes from the sort header text content,\n    // which doesn't give any indication that it performs a sorting operation.\n    this._sortActionDescription = 'Sort';\n    // Note that we use a string token for the `_columnDef`, because the value is provided both by\n    // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n    // and we want to avoid having the sort header depending on the CDK table because\n    // of this single reference.\n    if (!_sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortHeaderNotContainedWithinSortError();\n    }\n    if (defaultOptions?.arrowPosition) {\n      this.arrowPosition = defaultOptions?.arrowPosition;\n    }\n    this._handleStateChanges();\n  }\n  ngOnInit() {\n    if (!this.id && this._columnDef) {\n      this.id = this._columnDef.name;\n    }\n    // Initialize the direction of the arrow and set the view state to be immediately that state.\n    this._updateArrowDirection();\n    this._setAnimationTransitionState({\n      toState: this._isSorted() ? 'active' : this._arrowDirection\n    });\n    this._sort.register(this);\n    this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container');\n    this._updateSortActionDescription(this._sortActionDescription);\n  }\n  ngAfterViewInit() {\n    // We use the focus monitor because we also want to style\n    // things differently based on the focus origin.\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n      const newState = !!origin;\n      if (newState !== this._showIndicatorHint) {\n        this._setIndicatorHintVisible(newState);\n        this._changeDetectorRef.markForCheck();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._sort.deregister(this);\n    this._rerenderSubscription.unsubscribe();\n    if (this._sortButton) {\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n    }\n  }\n  /**\n   * Sets the \"hint\" state such that the arrow will be semi-transparently displayed as a hint to the\n   * user showing what the active sort will become. If set to false, the arrow will fade away.\n   */\n  _setIndicatorHintVisible(visible) {\n    // No-op if the sort header is disabled - should not make the hint visible.\n    if (this._isDisabled() && visible) {\n      return;\n    }\n    this._showIndicatorHint = visible;\n    if (!this._isSorted()) {\n      this._updateArrowDirection();\n      if (this._showIndicatorHint) {\n        this._setAnimationTransitionState({\n          fromState: this._arrowDirection,\n          toState: 'hint'\n        });\n      } else {\n        this._setAnimationTransitionState({\n          fromState: 'hint',\n          toState: this._arrowDirection\n        });\n      }\n    }\n  }\n  /**\n   * Sets the animation transition view state for the arrow's position and opacity. If the\n   * `disableViewStateAnimation` flag is set to true, the `fromState` will be ignored so that\n   * no animation appears.\n   */\n  _setAnimationTransitionState(viewState) {\n    this._viewState = viewState || {};\n    // If the animation for arrow position state (opacity/translation) should be disabled,\n    // remove the fromState so that it jumps right to the toState.\n    if (this._disableViewStateAnimation) {\n      this._viewState = {\n        toState: viewState.toState\n      };\n    }\n  }\n  /** Triggers the sort on this sort header and removes the indicator hint. */\n  _toggleOnInteraction() {\n    this._sort.sort(this);\n    // Do not show the animation if the header was already shown in the right position.\n    if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n      this._disableViewStateAnimation = true;\n    }\n  }\n  _handleClick() {\n    if (!this._isDisabled()) {\n      this._sort.sort(this);\n    }\n  }\n  _handleKeydown(event) {\n    if (!this._isDisabled() && (event.keyCode === SPACE || event.keyCode === ENTER)) {\n      event.preventDefault();\n      this._toggleOnInteraction();\n    }\n  }\n  /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n  _isSorted() {\n    return this._sort.active == this.id && (this._sort.direction === 'asc' || this._sort.direction === 'desc');\n  }\n  /** Returns the animation state for the arrow direction (indicator and pointers). */\n  _getArrowDirectionState() {\n    return `${this._isSorted() ? 'active-' : ''}${this._arrowDirection}`;\n  }\n  /** Returns the arrow position state (opacity, translation). */\n  _getArrowViewState() {\n    const fromState = this._viewState.fromState;\n    return (fromState ? `${fromState}-to-` : '') + this._viewState.toState;\n  }\n  /**\n   * Updates the direction the arrow should be pointing. If it is not sorted, the arrow should be\n   * facing the start direction. Otherwise if it is sorted, the arrow should point in the currently\n   * active sorted direction. The reason this is updated through a function is because the direction\n   * should only be changed at specific times - when deactivated but the hint is displayed and when\n   * the sort is active and the direction changes. Otherwise the arrow's direction should linger\n   * in cases such as the sort becoming deactivated but we want to animate the arrow away while\n   * preserving its direction, even though the next sort direction is actually different and should\n   * only be changed once the arrow displays again (hint or activation).\n   */\n  _updateArrowDirection() {\n    this._arrowDirection = this._isSorted() ? this._sort.direction : this.start || this._sort.start;\n  }\n  _isDisabled() {\n    return this._sort.disabled || this.disabled;\n  }\n  /**\n   * Gets the aria-sort attribute that should be applied to this sort header. If this header\n   * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n   * says that the aria-sort property should only be present on one header at a time, so removing\n   * ensures this is true.\n   */\n  _getAriaSortAttribute() {\n    if (!this._isSorted()) {\n      return 'none';\n    }\n    return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n  }\n  /** Whether the arrow inside the sort header should be rendered. */\n  _renderArrow() {\n    return !this._isDisabled() || this._isSorted();\n  }\n  _updateSortActionDescription(newDescription) {\n    // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n    // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n    // for every *cell* in the table, creating a lot of unnecessary noise.\n    // If _sortButton is undefined, the component hasn't been initialized yet so there's\n    // nothing to update in the DOM.\n    if (this._sortButton) {\n      // removeDescription will no-op if there is no existing message.\n      // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n      this._ariaDescriber?.describe(this._sortButton, newDescription);\n    }\n    this._sortActionDescription = newDescription;\n  }\n  /** Handles changes in the sorting state. */\n  _handleStateChanges() {\n    this._rerenderSubscription = merge(this._sort.sortChange, this._sort._stateChanges, this._intl.changes).subscribe(() => {\n      if (this._isSorted()) {\n        this._updateArrowDirection();\n        // Do not show the animation if the header was already shown in the right position.\n        if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n          this._disableViewStateAnimation = true;\n        }\n        this._setAnimationTransitionState({\n          fromState: this._arrowDirection,\n          toState: 'active'\n        });\n        this._showIndicatorHint = false;\n      }\n      // If this header was recently active and now no longer sorted, animate away the arrow.\n      if (!this._isSorted() && this._viewState && this._viewState.toState === 'active') {\n        this._disableViewStateAnimation = false;\n        this._setAnimationTransitionState({\n          fromState: 'active',\n          toState: this._arrowDirection\n        });\n      }\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  static {\n    this.ɵfac = function MatSortHeader_Factory(t) {\n      return new (t || MatSortHeader)(i0.ɵɵdirectiveInject(MatSortHeaderIntl), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MatSort, 8), i0.ɵɵdirectiveInject('MAT_SORT_HEADER_COLUMN_DEF', 8), i0.ɵɵdirectiveInject(i3.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.AriaDescriber, 8), i0.ɵɵdirectiveInject(MAT_SORT_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSortHeader,\n      selectors: [[\"\", \"mat-sort-header\", \"\"]],\n      hostAttrs: [1, \"mat-sort-header\"],\n      hostVars: 3,\n      hostBindings: function MatSortHeader_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatSortHeader_click_HostBindingHandler() {\n            return ctx._handleClick();\n          })(\"keydown\", function MatSortHeader_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          })(\"mouseenter\", function MatSortHeader_mouseenter_HostBindingHandler() {\n            return ctx._setIndicatorHintVisible(true);\n          })(\"mouseleave\", function MatSortHeader_mouseleave_HostBindingHandler() {\n            return ctx._setIndicatorHintVisible(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-sort\", ctx._getAriaSortAttribute());\n          i0.ɵɵclassProp(\"mat-sort-header-disabled\", ctx._isDisabled());\n        }\n      },\n      inputs: {\n        id: [i0.ɵɵInputFlags.None, \"mat-sort-header\", \"id\"],\n        arrowPosition: \"arrowPosition\",\n        start: \"start\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        sortActionDescription: \"sortActionDescription\",\n        disableClear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableClear\", \"disableClear\", booleanAttribute]\n      },\n      exportAs: [\"matSortHeader\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 7,\n      consts: [[1, \"mat-sort-header-container\", \"mat-focus-indicator\"], [1, \"mat-sort-header-content\"], [1, \"mat-sort-header-arrow\"], [1, \"mat-sort-header-stem\"], [1, \"mat-sort-header-indicator\"], [1, \"mat-sort-header-pointer-left\"], [1, \"mat-sort-header-pointer-right\"], [1, \"mat-sort-header-pointer-middle\"]],\n      template: function MatSortHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, MatSortHeader_Conditional_3_Template, 6, 6, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-sort-header-sorted\", ctx._isSorted())(\"mat-sort-header-position-before\", ctx.arrowPosition === \"before\");\n          i0.ɵɵattribute(\"tabindex\", ctx._isDisabled() ? null : 0)(\"role\", ctx._isDisabled() ? null : \"button\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(3, ctx._renderArrow() ? 3 : -1);\n        }\n      },\n      styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;color:var(--mat-sort-arrow-color);opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matSortAnimations.indicator, matSortAnimations.leftPointer, matSortAnimations.rightPointer, matSortAnimations.arrowOpacity, matSortAnimations.arrowPosition, matSortAnimations.allowChildren]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortHeader, [{\n    type: Component,\n    args: [{\n      selector: '[mat-sort-header]',\n      exportAs: 'matSortHeader',\n      host: {\n        'class': 'mat-sort-header',\n        '(click)': '_handleClick()',\n        '(keydown)': '_handleKeydown($event)',\n        '(mouseenter)': '_setIndicatorHintVisible(true)',\n        '(mouseleave)': '_setIndicatorHintVisible(false)',\n        '[attr.aria-sort]': '_getAriaSortAttribute()',\n        '[class.mat-sort-header-disabled]': '_isDisabled()'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [matSortAnimations.indicator, matSortAnimations.leftPointer, matSortAnimations.rightPointer, matSortAnimations.arrowOpacity, matSortAnimations.arrowPosition, matSortAnimations.allowChildren],\n      standalone: true,\n      template: \"<!--\\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\\n  will be read out as the user is navigating the table's cell (see #13012).\\n\\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\\n-->\\n<div class=\\\"mat-sort-header-container mat-focus-indicator\\\"\\n     [class.mat-sort-header-sorted]=\\\"_isSorted()\\\"\\n     [class.mat-sort-header-position-before]=\\\"arrowPosition === 'before'\\\"\\n     [attr.tabindex]=\\\"_isDisabled() ? null : 0\\\"\\n     [attr.role]=\\\"_isDisabled() ? null : 'button'\\\">\\n\\n  <!--\\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\\n  -->\\n  <div class=\\\"mat-sort-header-content\\\">\\n    <ng-content></ng-content>\\n  </div>\\n\\n  <!-- Disable animations while a current animation is running -->\\n  @if (_renderArrow()) {\\n    <div class=\\\"mat-sort-header-arrow\\\"\\n        [@arrowOpacity]=\\\"_getArrowViewState()\\\"\\n        [@arrowPosition]=\\\"_getArrowViewState()\\\"\\n        [@allowChildren]=\\\"_getArrowDirectionState()\\\"\\n        (@arrowPosition.start)=\\\"_disableViewStateAnimation = true\\\"\\n        (@arrowPosition.done)=\\\"_disableViewStateAnimation = false\\\">\\n      <div class=\\\"mat-sort-header-stem\\\"></div>\\n      <div class=\\\"mat-sort-header-indicator\\\" [@indicator]=\\\"_getArrowDirectionState()\\\">\\n        <div class=\\\"mat-sort-header-pointer-left\\\" [@leftPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n        <div class=\\\"mat-sort-header-pointer-right\\\" [@rightPointer]=\\\"_getArrowDirectionState()\\\"></div>\\n        <div class=\\\"mat-sort-header-pointer-middle\\\"></div>\\n      </div>\\n    </div>\\n  }\\n</div>\\n\",\n      styles: [\".mat-sort-header-container{display:flex;cursor:pointer;align-items:center;letter-spacing:normal;outline:0}[mat-sort-header].cdk-keyboard-focused .mat-sort-header-container,[mat-sort-header].cdk-program-focused .mat-sort-header-container{border-bottom:solid 1px currentColor}.mat-sort-header-disabled .mat-sort-header-container{cursor:default}.mat-sort-header-container::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-sort-header-content{text-align:center;display:flex;align-items:center}.mat-sort-header-position-before{flex-direction:row-reverse}.mat-sort-header-arrow{height:12px;width:12px;min-width:12px;position:relative;display:flex;color:var(--mat-sort-arrow-color);opacity:0}.mat-sort-header-arrow,[dir=rtl] .mat-sort-header-position-before .mat-sort-header-arrow{margin:0 0 0 6px}.mat-sort-header-position-before .mat-sort-header-arrow,[dir=rtl] .mat-sort-header-arrow{margin:0 6px 0 0}.mat-sort-header-stem{background:currentColor;height:10px;width:2px;margin:auto;display:flex;align-items:center}.cdk-high-contrast-active .mat-sort-header-stem{width:0;border-left:solid 2px}.mat-sort-header-indicator{width:100%;height:2px;display:flex;align-items:center;position:absolute;top:0;left:0}.mat-sort-header-pointer-middle{margin:auto;height:2px;width:2px;background:currentColor;transform:rotate(45deg)}.cdk-high-contrast-active .mat-sort-header-pointer-middle{width:0;height:0;border-top:solid 2px;border-left:solid 2px}.mat-sort-header-pointer-left,.mat-sort-header-pointer-right{background:currentColor;width:6px;height:2px;position:absolute;top:0}.cdk-high-contrast-active .mat-sort-header-pointer-left,.cdk-high-contrast-active .mat-sort-header-pointer-right{width:0;height:0;border-left:solid 6px;border-top:solid 2px}.mat-sort-header-pointer-left{transform-origin:right;left:0}.mat-sort-header-pointer-right{transform-origin:left;right:0}\"]\n    }]\n  }], () => [{\n    type: MatSortHeaderIntl\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: MatSort,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: ['MAT_SORT_HEADER_COLUMN_DEF']\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: i3.FocusMonitor\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i3.AriaDescriber,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_SORT_DEFAULT_OPTIONS]\n    }]\n  }], {\n    id: [{\n      type: Input,\n      args: ['mat-sort-header']\n    }],\n    arrowPosition: [{\n      type: Input\n    }],\n    start: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    sortActionDescription: [{\n      type: Input\n    }],\n    disableClear: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatSortModule {\n  static {\n    this.ɵfac = function MatSortModule_Factory(t) {\n      return new (t || MatSortModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSortModule,\n      imports: [MatCommonModule, MatSort, MatSortHeader],\n      exports: [MatSort, MatSortHeader]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n      imports: [MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSortModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatSort, MatSortHeader],\n      exports: [MatSort, MatSortHeader],\n      providers: [MAT_SORT_HEADER_INTL_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SORT_DEFAULT_OPTIONS, MAT_SORT_HEADER_INTL_PROVIDER, MAT_SORT_HEADER_INTL_PROVIDER_FACTORY, MatSort, MatSortHeader, MatSortHeaderIntl, MatSortModule, matSortAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,MAAM,CAAC,mBAAmB,EAAE;AAClC,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,wBAAwB,SAAS,oFAAoF;AACjI,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,6BAA6B,IAAI;AAAA,IAChE,CAAC,EAAE,uBAAuB,SAAS,mFAAmF;AACpH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,6BAA6B,KAAK;AAAA,IACjE,CAAC;AACD,IAAG,UAAU,GAAG,OAAO,CAAC;AACxB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAClD,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,iBAAiB,OAAO,mBAAmB,CAAC,EAAE,kBAAkB,OAAO,mBAAmB,CAAC,EAAE,kBAAkB,OAAO,wBAAwB,CAAC;AAC7J,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,cAAc,OAAO,wBAAwB,CAAC;AAC5D,IAAG,UAAU;AACb,IAAG,WAAW,gBAAgB,OAAO,wBAAwB,CAAC;AAC9D,IAAG,UAAU;AACb,IAAG,WAAW,iBAAiB,OAAO,wBAAwB,CAAC;AAAA,EACjE;AACF;AACA,SAAS,gCAAgC,IAAI;AAC3C,SAAO,MAAM,kDAAkD,EAAE,IAAI;AACvE;AAEA,SAAS,2CAA2C;AAClD,SAAO,MAAM,kFAAkF;AACjG;AAEA,SAAS,8BAA8B;AACrC,SAAO,MAAM,kDAAkD;AACjE;AAEA,SAAS,6BAA6B,WAAW;AAC/C,SAAO,MAAM,GAAG,SAAS,mDAAmD;AAC9E;AAGA,IAAM,2BAA2B,IAAI,eAAe,0BAA0B;AAE9E,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA,EAEZ,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,WAAW;AACvB,QAAI,aAAa,cAAc,SAAS,cAAc,WAAW,OAAO,cAAc,eAAe,YAAY;AAC/G,YAAM,6BAA6B,SAAS;AAAA,IAC9C;AACA,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,YAAY,iBAAiB;AAC3B,SAAK,kBAAkB;AACvB,SAAK,qBAAqB,IAAI,cAAc,CAAC;AAE7C,SAAK,YAAY,oBAAI,IAAI;AAEzB,SAAK,gBAAgB,IAAI,QAAQ;AAKjC,SAAK,QAAQ;AACb,SAAK,aAAa;AAElB,SAAK,WAAW;AAEhB,SAAK,aAAa,IAAI,aAAa;AAEnC,SAAK,cAAc,KAAK;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,UAAU;AACjB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,4BAA4B;AAAA,MACpC;AACA,UAAI,KAAK,UAAU,IAAI,SAAS,EAAE,GAAG;AACnC,cAAM,gCAAgC,SAAS,EAAE;AAAA,MACnD;AAAA,IACF;AACA,SAAK,UAAU,IAAI,SAAS,IAAI,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,UAAU;AACnB,SAAK,UAAU,OAAO,SAAS,EAAE;AAAA,EACnC;AAAA;AAAA,EAEA,KAAK,UAAU;AACb,QAAI,KAAK,UAAU,SAAS,IAAI;AAC9B,WAAK,SAAS,SAAS;AACvB,WAAK,YAAY,SAAS,QAAQ,SAAS,QAAQ,KAAK;AAAA,IAC1D,OAAO;AACL,WAAK,YAAY,KAAK,qBAAqB,QAAQ;AAAA,IACrD;AACA,SAAK,WAAW,KAAK;AAAA,MACnB,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,qBAAqB,UAAU;AAC7B,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAEA,UAAM,eAAe,UAAU,gBAAgB,KAAK,gBAAgB,CAAC,CAAC,KAAK,iBAAiB;AAC5F,QAAI,qBAAqB,sBAAsB,SAAS,SAAS,KAAK,OAAO,YAAY;AAEzF,QAAI,qBAAqB,mBAAmB,QAAQ,KAAK,SAAS,IAAI;AACtE,QAAI,sBAAsB,mBAAmB,QAAQ;AACnD,2BAAqB;AAAA,IACvB;AACA,WAAO,mBAAmB,kBAAkB;AAAA,EAC9C;AAAA,EACA,WAAW;AACT,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,KAAK;AAAA,EAC1B;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,SAAS;AAC5B,SAAK,mBAAmB,SAAS;AAAA,EACnC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,GAAG;AACtC,aAAO,KAAK,KAAK,UAAY,kBAAkB,0BAA0B,CAAC,CAAC;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,UAAU;AAAA,MACzB,QAAQ;AAAA,QACN,QAAQ,CAAI,WAAa,MAAM,iBAAiB,QAAQ;AAAA,QACxD,OAAO,CAAI,WAAa,MAAM,gBAAgB,OAAO;AAAA,QACrD,WAAW,CAAI,WAAa,MAAM,oBAAoB,WAAW;AAAA,QACjE,cAAc,CAAI,WAAa,4BAA4B,uBAAuB,gBAAgB,gBAAgB;AAAA,QAClH,UAAU,CAAI,WAAa,4BAA4B,mBAAmB,YAAY,gBAAgB;AAAA,MACxG;AAAA,MACA,SAAS;AAAA,QACP,YAAY;AAAA,MACd;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,oBAAoB;AAAA,IACjE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,MACX;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,SAAS,sBAAsB,OAAO,cAAc;AAClD,MAAI,YAAY,CAAC,OAAO,MAAM;AAC9B,MAAI,SAAS,QAAQ;AACnB,cAAU,QAAQ;AAAA,EACpB;AACA,MAAI,CAAC,cAAc;AACjB,cAAU,KAAK,EAAE;AAAA,EACnB;AACA,SAAO;AACT;AACA,IAAM,4BAA4B,mBAAmB,WAAW,MAAM,gBAAgB;AAKtF,IAAM,oBAAoB;AAAA;AAAA,EAExB,WAAW,QAAQ,aAAa;AAAA,IAAC,MAAM,mBAAmB,MAAM;AAAA,MAC9D,WAAW;AAAA,IACb,CAAC,CAAC;AAAA;AAAA,IAEF,MAAM,qBAAqB,MAAM;AAAA,MAC/B,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,IAAG,WAAW,8BAA8B,QAAQ,yBAAyB,CAAC;AAAA,EAAC,CAAC;AAAA;AAAA,EAElF,aAAa,QAAQ,eAAe,CAAC,MAAM,mBAAmB,MAAM;AAAA,IAClE,WAAW;AAAA,EACb,CAAC,CAAC,GAAG,MAAM,qBAAqB,MAAM;AAAA,IACpC,WAAW;AAAA,EACb,CAAC,CAAC,GAAG,WAAW,8BAA8B,QAAQ,yBAAyB,CAAC,CAAC,CAAC;AAAA;AAAA,EAElF,cAAc,QAAQ,gBAAgB,CAAC,MAAM,mBAAmB,MAAM;AAAA,IACpE,WAAW;AAAA,EACb,CAAC,CAAC,GAAG,MAAM,qBAAqB,MAAM;AAAA,IACpC,WAAW;AAAA,EACb,CAAC,CAAC,GAAG,WAAW,8BAA8B,QAAQ,yBAAyB,CAAC,CAAC,CAAC;AAAA;AAAA,EAElF,cAAc,QAAQ,gBAAgB;AAAA,IAAC,MAAM,yCAAyC,MAAM;AAAA,MAC1F,SAAS;AAAA,IACX,CAAC,CAAC;AAAA,IAAG,MAAM,mCAAmC,MAAM;AAAA,MAClD,SAAS;AAAA,IACX,CAAC,CAAC;AAAA,IAAG,MAAM,6EAA6E,MAAM;AAAA,MAC5F,SAAS;AAAA,IACX,CAAC,CAAC;AAAA;AAAA,IAEF,WAAW,0DAA0D,QAAQ,KAAK,CAAC;AAAA,IAAG,WAAW,WAAW,QAAQ,yBAAyB,CAAC;AAAA,EAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhJ,eAAe,QAAQ,iBAAiB;AAAA;AAAA,IAExC,WAAW,0CAA0C,QAAQ,2BAA2B,UAAU,CAAC,MAAM;AAAA,MACvG,WAAW;AAAA,IACb,CAAC,GAAG,MAAM;AAAA,MACR,WAAW;AAAA,IACb,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAA,IAEL,WAAW,0CAA0C,QAAQ,2BAA2B,UAAU,CAAC,MAAM;AAAA,MACvG,WAAW;AAAA,IACb,CAAC,GAAG,MAAM;AAAA,MACR,WAAW;AAAA,IACb,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAA,IAEL,WAAW,wCAAwC,QAAQ,2BAA2B,UAAU,CAAC,MAAM;AAAA,MACrG,WAAW;AAAA,IACb,CAAC,GAAG,MAAM;AAAA,MACR,WAAW;AAAA,IACb,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAA,IAEL,WAAW,wCAAwC,QAAQ,2BAA2B,UAAU,CAAC,MAAM;AAAA,MACrG,WAAW;AAAA,IACb,CAAC,GAAG,MAAM;AAAA,MACR,WAAW;AAAA,IACb,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,IAAG,MAAM,0EAA0E,MAAM;AAAA,MAC5F,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,IAAG,MAAM,sCAAsC,MAAM;AAAA,MACrD,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,IAAG,MAAM,mCAAmC,MAAM;AAAA,MAClD,WAAW;AAAA,IACb,CAAC,CAAC;AAAA,EAAC,CAAC;AAAA;AAAA,EAEJ,eAAe,QAAQ,iBAAiB,CAAC,WAAW,WAAW,CAAC,MAAM,MAAM,aAAa,GAAG;AAAA,IAC1F,UAAU;AAAA,EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP;AAMA,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,cAAc;AAKZ,SAAK,UAAU,IAAI,QAAQ;AAAA,EAC7B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAmB;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,SAAS,sCAAsC,YAAY;AACzD,SAAO,cAAc,IAAI,kBAAkB;AAC7C;AAEA,IAAM,gCAAgC;AAAA;AAAA,EAEpC,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,iBAAiB,CAAC;AAAA,EAC1D,YAAY;AACd;AAWA,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlB,IAAI,wBAAwB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,sBAAsB,OAAO;AAC/B,SAAK,6BAA6B,KAAK;AAAA,EACzC;AAAA,EACA,YAKA,OAAO,oBAGP,OAAO,YAAY,eAAe,aAClC,gBAAgB,gBAAgB;AAC9B,SAAK,QAAQ;AACb,SAAK,qBAAqB;AAC1B,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,iBAAiB;AAKtB,SAAK,qBAAqB;AAM1B,SAAK,aAAa,CAAC;AAEnB,SAAK,kBAAkB;AAIvB,SAAK,6BAA6B;AAElC,SAAK,gBAAgB;AAErB,SAAK,WAAW;AAIhB,SAAK,yBAAyB;AAK9B,QAAI,CAAC,UAAU,OAAO,cAAc,eAAe,YAAY;AAC7D,YAAM,yCAAyC;AAAA,IACjD;AACA,QAAI,gBAAgB,eAAe;AACjC,WAAK,gBAAgB,gBAAgB;AAAA,IACvC;AACA,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,WAAW;AACT,QAAI,CAAC,KAAK,MAAM,KAAK,YAAY;AAC/B,WAAK,KAAK,KAAK,WAAW;AAAA,IAC5B;AAEA,SAAK,sBAAsB;AAC3B,SAAK,6BAA6B;AAAA,MAChC,SAAS,KAAK,UAAU,IAAI,WAAW,KAAK;AAAA,IAC9C,CAAC;AACD,SAAK,MAAM,SAAS,IAAI;AACxB,SAAK,cAAc,KAAK,YAAY,cAAc,cAAc,4BAA4B;AAC5F,SAAK,6BAA6B,KAAK,sBAAsB;AAAA,EAC/D;AAAA,EACA,kBAAkB;AAGhB,SAAK,cAAc,QAAQ,KAAK,aAAa,IAAI,EAAE,UAAU,YAAU;AACrE,YAAM,WAAW,CAAC,CAAC;AACnB,UAAI,aAAa,KAAK,oBAAoB;AACxC,aAAK,yBAAyB,QAAQ;AACtC,aAAK,mBAAmB,aAAa;AAAA,MACvC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,SAAK,MAAM,WAAW,IAAI;AAC1B,SAAK,sBAAsB,YAAY;AACvC,QAAI,KAAK,aAAa;AACpB,WAAK,gBAAgB,kBAAkB,KAAK,aAAa,KAAK,sBAAsB;AAAA,IACtF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,SAAS;AAEhC,QAAI,KAAK,YAAY,KAAK,SAAS;AACjC;AAAA,IACF;AACA,SAAK,qBAAqB;AAC1B,QAAI,CAAC,KAAK,UAAU,GAAG;AACrB,WAAK,sBAAsB;AAC3B,UAAI,KAAK,oBAAoB;AAC3B,aAAK,6BAA6B;AAAA,UAChC,WAAW,KAAK;AAAA,UAChB,SAAS;AAAA,QACX,CAAC;AAAA,MACH,OAAO;AACL,aAAK,6BAA6B;AAAA,UAChC,WAAW;AAAA,UACX,SAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,WAAW;AACtC,SAAK,aAAa,aAAa,CAAC;AAGhC,QAAI,KAAK,4BAA4B;AACnC,WAAK,aAAa;AAAA,QAChB,SAAS,UAAU;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,uBAAuB;AACrB,SAAK,MAAM,KAAK,IAAI;AAEpB,QAAI,KAAK,WAAW,YAAY,UAAU,KAAK,WAAW,YAAY,UAAU;AAC9E,WAAK,6BAA6B;AAAA,IACpC;AAAA,EACF;AAAA,EACA,eAAe;AACb,QAAI,CAAC,KAAK,YAAY,GAAG;AACvB,WAAK,MAAM,KAAK,IAAI;AAAA,IACtB;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,CAAC,KAAK,YAAY,MAAM,MAAM,YAAY,SAAS,MAAM,YAAY,QAAQ;AAC/E,YAAM,eAAe;AACrB,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,MAAM,UAAU,KAAK,OAAO,KAAK,MAAM,cAAc,SAAS,KAAK,MAAM,cAAc;AAAA,EACrG;AAAA;AAAA,EAEA,0BAA0B;AACxB,WAAO,GAAG,KAAK,UAAU,IAAI,YAAY,EAAE,GAAG,KAAK,eAAe;AAAA,EACpE;AAAA;AAAA,EAEA,qBAAqB;AACnB,UAAM,YAAY,KAAK,WAAW;AAClC,YAAQ,YAAY,GAAG,SAAS,SAAS,MAAM,KAAK,WAAW;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,wBAAwB;AACtB,SAAK,kBAAkB,KAAK,UAAU,IAAI,KAAK,MAAM,YAAY,KAAK,SAAS,KAAK,MAAM;AAAA,EAC5F;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,MAAM,YAAY,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB;AACtB,QAAI,CAAC,KAAK,UAAU,GAAG;AACrB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,MAAM,aAAa,QAAQ,cAAc;AAAA,EACvD;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,CAAC,KAAK,YAAY,KAAK,KAAK,UAAU;AAAA,EAC/C;AAAA,EACA,6BAA6B,gBAAgB;AAM3C,QAAI,KAAK,aAAa;AAGpB,WAAK,gBAAgB,kBAAkB,KAAK,aAAa,KAAK,sBAAsB;AACpF,WAAK,gBAAgB,SAAS,KAAK,aAAa,cAAc;AAAA,IAChE;AACA,SAAK,yBAAyB;AAAA,EAChC;AAAA;AAAA,EAEA,sBAAsB;AACpB,SAAK,wBAAwB,MAAM,KAAK,MAAM,YAAY,KAAK,MAAM,eAAe,KAAK,MAAM,OAAO,EAAE,UAAU,MAAM;AACtH,UAAI,KAAK,UAAU,GAAG;AACpB,aAAK,sBAAsB;AAE3B,YAAI,KAAK,WAAW,YAAY,UAAU,KAAK,WAAW,YAAY,UAAU;AAC9E,eAAK,6BAA6B;AAAA,QACpC;AACA,aAAK,6BAA6B;AAAA,UAChC,WAAW,KAAK;AAAA,UAChB,SAAS;AAAA,QACX,CAAC;AACD,aAAK,qBAAqB;AAAA,MAC5B;AAEA,UAAI,CAAC,KAAK,UAAU,KAAK,KAAK,cAAc,KAAK,WAAW,YAAY,UAAU;AAChF,aAAK,6BAA6B;AAClC,aAAK,6BAA6B;AAAA,UAChC,WAAW;AAAA,UACX,SAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH;AACA,WAAK,mBAAmB,aAAa;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAkB,kBAAkB,iBAAiB,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,SAAS,CAAC,GAAM,kBAAkB,8BAA8B,CAAC,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,eAAe,CAAC,GAAM,kBAAkB,0BAA0B,CAAC,CAAC;AAAA,IACxX;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MACvC,WAAW,CAAC,GAAG,iBAAiB;AAAA,MAChC,UAAU;AAAA,MACV,cAAc,SAAS,2BAA2B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,yCAAyC;AACvE,mBAAO,IAAI,aAAa;AAAA,UAC1B,CAAC,EAAE,WAAW,SAAS,yCAAyC,QAAQ;AACtE,mBAAO,IAAI,eAAe,MAAM;AAAA,UAClC,CAAC,EAAE,cAAc,SAAS,8CAA8C;AACtE,mBAAO,IAAI,yBAAyB,IAAI;AAAA,UAC1C,CAAC,EAAE,cAAc,SAAS,8CAA8C;AACtE,mBAAO,IAAI,yBAAyB,KAAK;AAAA,UAC3C,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,IAAI,sBAAsB,CAAC;AACvD,UAAG,YAAY,4BAA4B,IAAI,YAAY,CAAC;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,IAAI,CAAI,WAAa,MAAM,mBAAmB,IAAI;AAAA,QAClD,eAAe;AAAA,QACf,OAAO;AAAA,QACP,UAAU,CAAI,WAAa,4BAA4B,YAAY,YAAY,gBAAgB;AAAA,QAC/F,uBAAuB;AAAA,QACvB,cAAc,CAAI,WAAa,4BAA4B,gBAAgB,gBAAgB,gBAAgB;AAAA,MAC7G;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,mBAAmB;AAAA,MAC9D,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,6BAA6B,qBAAqB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,uBAAuB,GAAG,CAAC,GAAG,sBAAsB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,GAAG,+BAA+B,GAAG,CAAC,GAAG,gCAAgC,CAAC;AAAA,MAC/S,UAAU,SAAS,uBAAuB,IAAI,KAAK;AACjD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,OAAO,CAAC;AACrE,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,IAAI,UAAU,CAAC,EAAE,mCAAmC,IAAI,kBAAkB,QAAQ;AAC3H,UAAG,YAAY,YAAY,IAAI,YAAY,IAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,YAAY,IAAI,OAAO,QAAQ;AACpG,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,aAAa,IAAI,IAAI,EAAE;AAAA,QACjD;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,o2DAAo2D;AAAA,MAC72D,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,kBAAkB,WAAW,kBAAkB,aAAa,kBAAkB,cAAc,kBAAkB,cAAc,kBAAkB,eAAe,kBAAkB,aAAa;AAAA,MAC1M;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,WAAW;AAAA,QACX,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,QACpB,oCAAoC;AAAA,MACtC;AAAA,MACA,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,kBAAkB,WAAW,kBAAkB,aAAa,kBAAkB,cAAc,kBAAkB,cAAc,kBAAkB,eAAe,kBAAkB,aAAa;AAAA,MACzM,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ,CAAC,o2DAAo2D;AAAA,IAC/2D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,SAAS,aAAa;AAAA,MACjD,SAAS,CAAC,SAAS,aAAa;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,6BAA6B;AAAA,MACzC,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,SAAS,aAAa;AAAA,MACjD,SAAS,CAAC,SAAS,aAAa;AAAA,MAChC,WAAW,CAAC,6BAA6B;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}