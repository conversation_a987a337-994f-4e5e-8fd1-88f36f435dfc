/* Container and Layout */
.container {
  padding: 12px;
  max-width: 100vw;
  margin: 0;
  height: 93vh;
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
}

.mat-mdc-card-header {
    display: flex;
    padding: 0px 16px 0;
}

/* Header Section */
.header-section {
  margin-bottom: 3px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  background-color: #f5f5f5;
  color: #666;
}

.back-button:hover {
  background-color: #e0e0e0;
  color: #333;
}

.title-content h2 {
  margin: 0 0 2px 0;
  color: #333;
  font-size: 22px;
  font-weight: 500;
}

.title-content p {
  margin: 0;
  color: #666;
  font-size: 13px;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #666;
}

.loading-container p {
  margin-top: 12px;
  font-size: 14px;
}

/* Details Section */
.details-section {
  margin-top: 8px;
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
}

.details-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.card-title-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.card-title-space {
  margin-right: 20px;
}
.badges {
  display: flex;
  gap: 8px;
}

/* Action Buttons Section */
.action-buttons-section {
  padding: 8px 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  flex-shrink: 0;
}

.action-buttons-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  flex-wrap: nowrap;
  min-width: fit-content;
}

.bank-account-section {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  flex: 1;
  min-width: 280px;
}

.bank-account-field {
  min-width: 220px;
  flex: 1;
  margin-bottom: -15px;
}

.bank-account-field .mat-mdc-form-field-wrapper {
  padding-bottom: 0;
}

.change-account-btn {
  height: 48px;
  min-width: 140px;
  white-space: nowrap;
  margin-bottom: 1.34375em;
  font-size: 13px;
}

.authorize-btn {
  height: 48px;
  min-width: 120px;
  background-color: #4caf50;
  color: white;
  margin-bottom: 1.34375em;
  font-size: 13px;
}

.authorize-btn:hover:not(:disabled) {
  background-color: #45a049;
}

.authorize-btn:disabled {
  background-color: #cccccc;
  color: #666666;
}

.update-efts-btn {
  height: 48px;
  min-width: 140px;
  background-color: #ff9800;
  color: white;
  margin-bottom: 1.34375em;
  font-size: 13px;
}

.update-efts-btn:hover {
  background-color: #f57c00;
}

/* Details Tabs */
.details-tabs {
  margin-top: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.details-tabs .mat-mdc-tab-group {
  --mdc-tab-indicator-active-indicator-color: #1976d2;
  --mdc-secondary-navigation-tab-container-height: 60px;
  --mat-tab-header-label-text-size: 20px;
  --mat-tab-header-label-text-weight: 800;
}

.details-tabs .mat-mdc-tab {
  min-width: 200px;
  font-weight: 800 !important;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  font-size: 20px !important;
  height: 60px;
}

.details-tabs .mat-mdc-tab .mdc-tab__text-label {
  font-weight: 800 !important;
  font-size: 20px !important;
}

/* Force override Material Design tab label styles */
.details-tabs .mat-mdc-tab .mdc-tab__content .mdc-tab__text-label {
  font-size: 20px !important;
  font-weight: 800 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.8px !important;
}

.details-tabs .mat-mdc-tab-label {
  font-size: 20px !important;
  font-weight: 800 !important;
}

.details-tabs .mat-mdc-tab-label-content {
  font-size: 20px !important;
  font-weight: 800 !important;
}

/* Deep override for Angular Material encapsulation */
::ng-deep .details-tabs .mat-mdc-tab .mdc-tab__text-label {
  font-size: 20px !important;
  font-weight: 800 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.8px !important;
}

::ng-deep .details-tabs .mat-mdc-tab-label {
  font-size: 20px !important;
  font-weight: 800 !important;
}

::ng-deep .details-tabs .mat-mdc-tab-label-content {
  font-size: 20px !important;
  font-weight: 800 !important;
}

.details-tabs .mat-mdc-tab-body-wrapper {
  flex-grow: 1;
}

.details-tabs .mat-mdc-tab-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.tab-content {
  padding: 24px 20px;
  background: #f8f9fa;
  min-height: 250px;
  border-radius: 0;
}

.section-title {
  margin: 0 0 8px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 4px;
}

.detail-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 8px 0;
  flex-wrap: wrap;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row label {
  font-weight: 700;
  color: #333;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  min-width: 140px;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.4;
  flex: 1;
}

.description-text {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  line-height: 1.5;
  font-size: 16px !important;
  font-weight: 600 !important;
}

.amount-highlight {
  color: #2e7d32 !important;
  font-size: 22px !important;
  font-weight: 800 !important;
}

.document-link {
  color: #1976d2 !important;
  text-decoration: underline;
  cursor: pointer;
  font-size: 18px !important;
  font-weight: 700 !important;
}

.document-link:hover {
  color: #1565c0 !important;
}

/* Type Badges */
.type-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
}

.type-claims {
  background-color: #e3f2fd;
  color: #1565c0;
  border: 1px solid #bbdefb;
}

.type-commissions {
  background-color: #f3e5f5;
  color: #7b1fa2;
  border: 1px solid #e1bee7;
}

.type-policy_maturity {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

/* Card Actions */
.card-actions {
  padding: 16px 24px;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

/* Error Section */
.error-section {
  margin-top: 40px;
}

.error-card {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border: 1px solid #feb2b2;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40px 20px;
}

.error-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: #e53e3e;
  margin-bottom: 16px;
}

.error-content h3 {
  margin: 0 0 12px 0;
  color: #742a2a;
  font-size: 24px;
  font-weight: 500;
}

.error-content p {
  margin: 0 0 24px 0;
  color: #9c4221;
  font-size: 16px;
  line-height: 1.5;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .container {
    padding: 8px;
    height: 100vh;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .title-section {
    gap: 8px;
  }

  .title-content h2 {
    font-size: 18px;
  }

  .card-title-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .badges {
    align-self: flex-start;
  }

  .details-tabs .mat-mdc-tab {
    min-width: 160px;
    font-size: 16px !important;
    font-weight: 800 !important;
    height: 56px;
  }

  .details-tabs .mat-mdc-tab .mdc-tab__text-label {
    font-weight: 800 !important;
    font-size: 16px !important;
  }

  .details-tabs .mat-mdc-tab .mdc-tab__content .mdc-tab__text-label {
    font-size: 16px !important;
    font-weight: 800 !important;
  }

  .details-tabs .mat-mdc-tab-label {
    font-size: 16px !important;
    font-weight: 800 !important;
  }

  .details-tabs .mat-mdc-tab-label-content {
    font-size: 16px !important;
    font-weight: 800 !important;
  }

  .details-tabs .mat-mdc-tab-group {
    --mdc-secondary-navigation-tab-container-height: 56px;
    --mat-tab-header-label-text-size: 16px;
    --mat-tab-header-label-text-weight: 800;
  }

  ::ng-deep .details-tabs .mat-mdc-tab .mdc-tab__text-label {
    font-size: 16px !important;
    font-weight: 800 !important;
  }

  ::ng-deep .details-tabs .mat-mdc-tab-label {
    font-size: 16px !important;
    font-weight: 800 !important;
  }

  .tab-content {
    padding: 16px 12px;
    min-height: 150px;
  }

  .detail-row {
    flex-direction: row;
    gap: 8px;
    margin-bottom: 12px;
  }

  .detail-row label {
    min-width: 120px;
    font-size: 12px;
  }

  .detail-value {
    font-size: 16px;
  }

  .amount-highlight {
    font-size: 20px !important;
  }

  .action-buttons-container {
    flex-wrap: nowrap;
    gap: 8px;
    min-width: 600px; /* Force horizontal scroll for buttons */
  }

  .bank-account-section {
    min-width: 200px;
  }

  .bank-account-field {
    min-width: 180px;
  }

  .change-account-btn,
  .authorize-btn,
  .update-efts-btn {
    min-width: 100px;
    font-size: 12px;
    height: 44px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 6px;
  }

  .title-content h2 {
    font-size: 16px;
  }

  .detail-row {
    margin-bottom: 6px;
  }

  .type-badge {
    padding: 3px 6px;
    font-size: 10px;
  }

  .details-tabs .mat-mdc-tab {
    min-width: 140px;
    font-size: 14px !important;
    font-weight: 800 !important;
    height: 52px;
  }

  .details-tabs .mat-mdc-tab .mdc-tab__text-label {
    font-weight: 800 !important;
    font-size: 14px !important;
  }

  .details-tabs .mat-mdc-tab .mdc-tab__content .mdc-tab__text-label {
    font-size: 14px !important;
    font-weight: 800 !important;
  }

  .details-tabs .mat-mdc-tab-label {
    font-size: 14px !important;
    font-weight: 800 !important;
  }

  .details-tabs .mat-mdc-tab-label-content {
    font-size: 14px !important;
    font-weight: 800 !important;
  }

  .details-tabs .mat-mdc-tab-group {
    --mdc-secondary-navigation-tab-container-height: 52px;
    --mat-tab-header-label-text-size: 14px;
    --mat-tab-header-label-text-weight: 800;
  }

  ::ng-deep .details-tabs .mat-mdc-tab .mdc-tab__text-label {
    font-size: 14px !important;
    font-weight: 800 !important;
  }

  ::ng-deep .details-tabs .mat-mdc-tab-label {
    font-size: 14px !important;
    font-weight: 800 !important;
  }

  .tab-content {
    padding: 12px 8px;
    min-height: 120px;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-bottom: 10px;
  }

  .detail-row label {
    min-width: unset;
    font-size: 11px;
  }

  .detail-value {
    font-size: 15px;
  }

  .amount-highlight {
    font-size: 18px !important;
  }

  .action-buttons-container {
    min-width: 500px;
  }

  .error-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
  }

  .error-content h3 {
    font-size: 18px;
  }

  .error-content p {
    font-size: 13px;
  }
}

/* Authorization Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(2px);
}

.authorization-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 24px 5px 24px;
  border-bottom: 2px solid #e0e0e0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-icon {
  color: #4caf50;
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.close-button {
  color: #666;
}

.close-button:hover {
  background-color: #f5f5f5;
  color: #333;
}

.modal-body {
  padding: 15px;
}

.authorization-details {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.warning-section {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.warning-icon {
  color: #856404;
  font-size: 24px;
  width: 24px;
  height: 24px;
  margin-top: 2px;
}

.warning-section p {
  margin: 0;
  color: #856404;
  font-size: 14px;
  line-height: 1.5;
}

.payment-summary {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.payment-summary h4 {
  margin: 0 0 16px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.summary-grid {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-row label {
  font-weight: 600;
  color: #6c757d;
  font-size: 14px;
  min-width: 120px;
}

.summary-row .value {
  color: #333;
  font-size: 14px;
  text-align: right;
  flex: 1;
}

.payee-highlight {
  font-weight: 600;
  color: #1976d2;
}

.confirmation-section {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
}

.check-icon {
  color: #155724;
  font-size: 24px;
  width: 24px;
  height: 24px;
  margin-top: 2px;
}

.confirmation-section p {
  margin: 0;
  color: #155724;
  font-size: 14px;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 10px 24px 10px 24px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.cancel-button {
  color: #6c757d;
}

.cancel-button:hover {
  background-color: #e9ecef;
  color: #495057;
}

.authorize-button {
  background-color: #4caf50;
  color: white;
  font-weight: 600;
}

.authorize-button:hover {
  background-color: #45a049;
}

/* Mobile Responsiveness for Modal */
@media (max-width: 768px) {
  .authorization-modal {
    width: 95%;
    margin: 20px;
    max-height: 85vh;
  }

  .modal-header {
    padding: 20px 16px 12px 16px;
  }

  .modal-header h3 {
    font-size: 18px;
  }

  .modal-body {
    padding: 20px 16px;
  }

  .authorization-details {
    gap: 20px;
  }

  .payment-summary {
    padding: 16px;
  }

  .summary-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    padding: 12px 0;
  }

  .summary-row label {
    min-width: unset;
    font-size: 13px;
  }

  .summary-row .value {
    text-align: left;
    font-size: 14px;
    font-weight: 500;
  }

  .modal-footer {
    padding: 12px 16px 20px 16px;
    flex-direction: column;
    gap: 8px;
  }

  .cancel-button,
  .authorize-button {
    width: 100%;
    height: 48px;
  }
}

@media (max-width: 480px) {
  .authorization-modal {
    width: 98%;
    margin: 10px;
  }

  .warning-section,
  .confirmation-section {
    padding: 12px;
  }

  .warning-section p,
  .confirmation-section p {
    font-size: 13px;
  }

  .payment-summary h4 {
    font-size: 15px;
  }
}