.home-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  margin-bottom: 20px;
}

.welcome-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
}

.welcome-card mat-card-title mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-content mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 500;
}

.stat-info p {
  margin: 0;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}
