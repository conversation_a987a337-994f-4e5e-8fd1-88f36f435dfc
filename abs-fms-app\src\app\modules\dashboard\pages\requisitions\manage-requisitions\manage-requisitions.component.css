td.mat-cell:last-child,
th.mat-header-cell:last-child {
  width: 280px; /* or 250px */
  max-width: 280px;
  white-space: nowrap;
}

/* Manage Requisitions Component Styles */
.manage-requisitions-container {
  padding: 20px;
  background-color: #f5f5f5;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white; 
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex-wrap: wrap;
  gap: 16px;
}

.title-section h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.title-section p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* Search and Filter Section */
.search-filter-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 24px;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-controls {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
}

.filter-field {
  min-width: 180px;
  flex: 1;
}

.search-field {
  flex: 2;
  min-width: 350px;
}

.clear-button {
  height: 56px;
  min-width: 140px;
}

/* Autocomplete Styles */
.autocomplete-option {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  line-height: 1.4;
}

.autocomplete-option:last-child {
  border-bottom: none;
}

.autocomplete-option:hover {
  background-color: #f8f9fa;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.code {
  font-weight: 600;
  color: #1976d2;
  font-size: 14px;
}

.type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.type-supplier {
  background-color: #e3f2fd;
  color: #1976d2;
}

.type-medical_provider {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.type-contractor {
  background-color: #fff3e0;
  color: #f57c00;
}

.type-employee {
  background-color: #e8f5e8;
  color: #388e3c;
}

.option-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.payee {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.amount {
  font-size: 14px;
  color: #2e7d32;
  font-weight: 600;
}

.option-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.invoice {
  font-size: 12px;
  color: #666;
}

.status {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-approved {
  background-color: #d4edda;
  color: #155724;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.option-narrative {
  margin: 6px 0;
}

.narrative {
  font-size: 12px;
  color: #666;
  font-style: italic;
  line-height: 1.3;
}

.option-dates {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 6px;
}

.created-date,
.created-by {
  font-size: 11px;
  color: #888;
}

.created-date {
  font-weight: 500;
}

.created-by {
  font-style: italic;
}

/* Search Instructions */
.search-instructions {
  margin-top: 24px;
}

.instructions-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
}

.instructions-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.instructions-icon {
  color: #6c757d;
  font-size: 32px;
  width: 32px;
  height: 32px;
  margin-top: 4px;
}

.instructions-text h3 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
}

.instructions-text p {
  margin: 0 0 12px 0;
  color: #6c757d;
  line-height: 1.5;
}

.instructions-text ul {
  margin: 0 0 12px 0;
  padding-left: 20px;
  color: #6c757d;
}

.instructions-text li {
  margin-bottom: 4px;
  line-height: 1.4;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .manage-requisitions-container {
    padding: 16px;
  }

  .header-section {
    padding: 16px;
    margin-bottom: 20px;
  }

  .title-section h2 {
    font-size: 24px;
  }

  .search-filter-section {
    padding: 16px;
    margin-bottom: 20px;
  }

  .filter-controls {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-field {
    max-width: none;
    min-width: unset;
  }

  .clear-button {
    width: 100%;
    height: 48px;
  }

  .search-field {
    max-width: none;
  }

  .instructions-content {
    flex-direction: column;
    gap: 12px;
  }

  .instructions-icon {
    align-self: flex-start;
  }

  .autocomplete-option {
    padding: 8px 0;
  }

  .option-header,
  .option-details,
  .option-meta,
  .option-dates {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .option-narrative {
    margin: 4px 0;
  }

  .narrative {
    font-size: 11px;
  }

  .created-date,
  .created-by {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .manage-requisitions-container {
    padding: 12px;
  }

  .header-section {
    padding: 12px;
  }

  .title-section h2 {
    font-size: 20px;
  }

  .search-filter-section {
    padding: 12px;
  }

  .instructions-text h3 {
    font-size: 16px;
  }

  .instructions-text p,
  .instructions-text li {
    font-size: 14px;
  }
}


.action-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.action-buttons button {
  min-width: 180px;
  height: 47px;
  color: white;
  background-color: #900000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  text-transform: none;
  border-radius: 6px;
}

.action-buttons button mat-icon {
  margin-right: 8px;
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.subheader{
  display: flex;
  justify-content: space-between;
  align-items:flex-start;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}
.content-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-top: 20px;
}

/* Search Section */
.search-section {
  margin-bottom: 24px;
  display: flex;
  justify-content: flex-start;
}

.search-field {
  width: 300px;
  max-width: 400px;
  min-width: 250px;
}

/* Table Container */
.table-container {
  width: 100%;
  overflow-x: auto;
}

.requisitions-table {
  width: 100%;
  background: white;
}

.requisitions-table .mat-mdc-header-cell {
  font-weight: 600;
  color: #333;
  background-color: #f8f9fa;
}

.requisitions-table .mat-mdc-cell {
  padding: 12px 8px;
  border-bottom: 1px solid #e0e0e0;
}

.requisitions-table .mat-mdc-row:hover {
  background-color: #f5f5f5;
}

/* Table Cell Styles */
.amount-cell {
  font-weight: 600;
  color: #2e7d32;
}

.narrative-cell {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-draft {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-pending {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-approved {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-rejected {
  background-color: #ffebee;
  color: #d32f2f;
}



/* Table Action Buttons */
.table-action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  min-width: 150px;
}


.btn {
  color: #fff;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 6px;
}


.table-action-buttons .action-btn,
.action-buttons .action-btn {
  width: 30px;
  height: 28px;
  min-width: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 11px;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  flex-shrink: 0;
}

.table-action-buttons .action-btn:hover,
.action-buttons .action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.table-action-buttons .action-btn mat-icon,
.action-buttons .action-btn mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin: 0;
}

/* No Data Message */
.no-data-message {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-data-message mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #ccc;
  margin-bottom: 16px;
}

.no-data-message p {
  margin: 8px 0;
}

.no-data-subtitle {
  font-size: 0.9rem;
  color: #999;
}

/* View Details Modal */
.view-details-modal {
  max-width: 700px;
  width: 95%;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 8px 0;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  color: #333;
  font-size: 1rem;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #e0e0e0;
}

.amount-highlight {
  background-color: #e8f5e8;
  border-left-color: #4caf50;
  font-weight: 600;
  color: #2e7d32;
}

/* Edit Form Styles */
.edit-form {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16px 24px;
}

.edit-form .form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.edit-form .form-row:last-child {
  margin-bottom: 0;
}

.edit-form .full-width {
  width: 100%;
}

.edit-form .half-width {
  width: calc(50% - 8px);
}

/* Modal Footer Button Styling */
.modal-footer button {
  margin-left: 8px;
  background-color: #900000;
  color: white;
}

.modal-footer button:first-child {
  margin-left: 0;
}

.modal-footer button mat-icon {
  margin-right: 8px;
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Responsive adjustments for view details */
@media (max-width: 768px) {
  .search-field {
    width: 100%;
    min-width: unset;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .view-details-modal {
    width: 98%;
    margin: 10px;
  }

  .edit-form .form-row {
    flex-direction: column;
  }

  .edit-form .half-width {
    width: 100%;
  }

  .modal-footer {
    flex-direction: column;
    gap: 8px;
  }

  .modal-footer button {
    margin-left: 0;
    width: 100%;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}



.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  background-color: #fafafa;
}

/* Form Styles */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.full-width {
  width: 100%;
}

.half-width {
  width: calc(50% - 8px);
}

/* Material Form Field Customization */
.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-form-field + .mat-mdc-form-field {
  margin-left: 0;
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
  .create-requisitions-container {
    padding: 12px;
  }

  .header-section {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 16px;
    margin-bottom: 20px;
  }

  .title-section h2 {
    font-size: 24px;
  }

  .title-section p {
    font-size: 14px;
  }

  .action-buttons {
    flex-direction: row;
    justify-content: center;
    width: 100%;
    gap: 10px;
  }

  .action-buttons button {
    min-width: 140px;
    height: 40px;
    font-size: 13px;
  }

  .content-section {
    padding: 16px;
  }

  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .requisitions-table {
    min-width: 800px;
  }

  .table-action-buttons {
    min-width: 120px;
    gap: 2px;
  }

  .table-action-buttons .action-btn {
    width: 28px;
    height: 26px;
    min-width: 28px;
  }

  .table-action-buttons .action-btn mat-icon {
    font-size: 14px;
    width: 14px;
    height: 14px;
  }

  .modal-content {
    width: 95%;
    margin: 10px;
    max-height: 95vh;
  }

  .modal-header {
    padding: 16px;
  }

  .modal-body {
    padding: 16px;
  }

  .modal-footer {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
  }

  .modal-footer button {
    width: 100%;
    margin-left: 0;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .half-width {
    width: 100%;
  }

  /* Table cell adjustments for mobile */
  .requisitions-table .mat-mdc-cell,
  .requisitions-table .mat-mdc-header-cell {
    padding: 8px 4px;
    font-size: 0.9rem;
  }

  .narrative-cell {
    max-width: 150px;
  }

  .status-badge {
    font-size: 0.7rem;
    padding: 2px 8px;
  }
}

@media (max-width: 480px) {
  .create-requisitions-container {
    padding: 8px;
  }

  .header-section {
    padding: 12px;
    margin-bottom: 16px;
  }

  .title-section h2 {
    font-size: 20px;
  }

  .title-section p {
    font-size: 13px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons button {
    width: 100%;
    min-width: unset;
    height: 36px;
    font-size: 12px;
  }

  .content-section {
    padding: 12px;
  }

  .search-field {
    width: 100%;
  }

  .modal-content {
    width: 98%;
    margin: 5px;
  }

  .modal-header {
    padding: 12px;
  }

  .modal-header h3 {
    font-size: 18px;
  }

  .modal-body {
    padding: 12px;
  }

  .modal-footer {
    padding: 10px 12px;
  }

  .table-action-buttons .action-btn {
    width: 26px;
    height: 24px;
    min-width: 26px;
  }

  .table-action-buttons .action-btn mat-icon {
    font-size: 12px;
    width: 12px;
    height: 12px;
  }

  /* Stack table columns on very small screens */
  .requisitions-table {
    min-width: 600px;
  }

  .requisitions-table .mat-mdc-cell,
  .requisitions-table .mat-mdc-header-cell {
    padding: 6px 2px;
    font-size: 0.8rem;
  }
}

/* Payee Autocomplete Styles */
.payee-option {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

.payee-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.payee-details {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.create-payee-option {
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.create-payee-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1976d2;
  font-weight: 500;
  padding: 4px 0;
}

.create-payee-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.create-payee-option:hover {
  background-color: #e3f2fd;
}

/* Autocomplete panel styling */
.mat-mdc-autocomplete-panel {
  max-height: 300px;
}

.mat-mdc-option {
  line-height: normal;
  height: auto;
  min-height: 48px;
  padding: 8px 16px;
}

.mat-mdc-option:hover {
  background-color: #f5f5f5;
}

/* Auto-populated field styles */
.auto-populated {
  position: relative;
}

.auto-populated .mat-mdc-form-field-flex {
  background-color: #f8f9fa;
  border-left: 3px solid #4caf50;
}

.auto-populated .mat-mdc-form-field-outline-start,
.auto-populated .mat-mdc-form-field-outline-end {
  border-color: #4caf50 !important;
}

.auto-populated .mat-mdc-form-field-outline-thick .mat-mdc-form-field-outline-start,
.auto-populated .mat-mdc-form-field-outline-thick .mat-mdc-form-field-outline-end {
  border-color: #4caf50 !important;
  border-width: 2px !important;
}

.auto-populated input,
.auto-populated .mat-mdc-select {
  color: #2e7d32;
  font-weight: 500;
}

.auto-populated .mat-mdc-form-field-label {
  color: #4caf50 !important;
}

.auto-populated-hint {
  color: #4caf50 !important;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  font-weight: 500;
}

.auto-populated-hint mat-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

/* Disabled field styling for auto-populated fields */
.auto-populated input:disabled,
.auto-populated .mat-mdc-select-disabled {
  color: #2e7d32 !important;
  -webkit-text-fill-color: #2e7d32 !important;
  background-color: transparent;
}

.auto-populated .mat-mdc-select-arrow {
  color: #4caf50;
}

/* Clear button for payee field */
.payee-clear-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.payee-clear-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

.payee-clear-button mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Button Hover Effects */
.action-buttons button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

/* Form Validation Styles */
.mat-mdc-form-field.mat-form-field-invalid .mat-mdc-text-field-wrapper {
  border-color: #f44336;
}

/* Modal Animation */
.modal-overlay {
  animation: fadeIn 0.3s ease;
}

.modal-content {
  animation: slideIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}