<div class="create-requisitions-container ">
   <!-- Header with Action Buttons -->
  <div class="header-section">
    <div class="title-section">
      <h2>Create Requisitions</h2>
    </div>
  </div>

  <!-- Create Requisitions Form -->
  <form class="form-body" [formGroup]="requisitionForm">
    <div class="form-row">
      <mat-form-field appearance="outline" class="full-width" [class.auto-populated]="selectedPayee">
        <mat-label>Payee *</mat-label>
        <input matInput formControlName="payee" placeholder="Search or select payee"
          [matAutocomplete]="payeeAutocomplete" (blur)="onPayeeChange(); onRequisitionPayeeNameChange()">
        <button matSuffix mat-icon-button type="button" class="payee-clear-button" *ngIf="selectedPayee"
          (click)="clearPayeeInformation()" matTooltip="Clear selected payee">
          <mat-icon>clear</mat-icon>
        </button>
        <mat-autocomplete #payeeAutocomplete="matAutocomplete" [displayWith]="displayPayeeName"
          (optionSelected)="onPayeeSelected($event.option.value)">
          <mat-option *ngFor="let payee of filteredPayees | async" [value]="payee">
            <div class="payee-option">
              <div class="payee-name">{{payee.name}}</div>
              <div class="payee-details">{{payee.pinNo}} | {{payee.bankName}}</div>
            </div>
          </mat-option>
        </mat-autocomplete>
        <mat-hint *ngIf="!selectedPayee">Search by name, PIN, or bank name</mat-hint>
        <mat-hint *ngIf="selectedPayee" class="auto-populated-hint">
          <mat-icon>check_circle</mat-icon>
          Payee selected - information auto-populated
        </mat-hint>
        <mat-error *ngIf="hasError('payee')">
          {{ getErrorMessage('payee') }}
        </mat-error>
      </mat-form-field>
    </div>
    <div class="form-row">
      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Code</mat-label>
        <input matInput formControlName="code" placeholder="Enter requisition code">
        <mat-error *ngIf="hasError('code')">
          {{ getErrorMessage('code') }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="half-width"
        [class.auto-populated]="requisitionForm.get('chequePayee')?.disabled">
        <mat-label>Input Cheque Payee</mat-label>
        <input matInput formControlName="chequePayee" placeholder="Enter payee name">
        <mat-hint *ngIf="requisitionForm.get('chequePayee')?.disabled" class="auto-populated-hint">
          <mat-icon>info</mat-icon>
          Auto-populated from selected payee
        </mat-hint>
        <mat-error *ngIf="hasError('chequePayee')">
          {{ getErrorMessage('chequePayee') }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="half-width"
        [class.auto-populated]="requisitionForm.get('payeeBankBranch')?.disabled">
        <mat-label>Payee Bank Branch</mat-label>
        <mat-select formControlName="payeeBankBranch">
          <mat-option value="">-- Select Bank Branch --</mat-option>
          <mat-option *ngFor="let branch of bankBranches;" [value]="branch.value">
            {{branch.name}}
          </mat-option>
        </mat-select>
        <mat-hint *ngIf="!requisitionForm.get('payeeBankBranch')?.disabled">Select the bank branch</mat-hint>
        <mat-hint *ngIf="requisitionForm.get('payeeBankBranch')?.disabled" class="auto-populated-hint">
          <mat-icon>info</mat-icon>
          Auto-populated from selected payee
        </mat-hint>
      </mat-form-field>

      <mat-form-field appearance="outline" class="half-width"
        [class.auto-populated]="requisitionForm.get('payeeAccountNo')?.disabled">
        <mat-label>Payee Account No</mat-label>
        <input matInput formControlName="payeeAccountNo" placeholder="Enter account number">
        <mat-hint *ngIf="!requisitionForm.get('payeeAccountNo')?.disabled">Enter the account number</mat-hint>
        <mat-hint *ngIf="requisitionForm.get('payeeAccountNo')?.disabled" class="auto-populated-hint">
          <mat-icon>info</mat-icon>
          Auto-populated from selected payee
        </mat-hint>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Narrative</mat-label>
        <textarea matInput formControlName="narrative" rows="3" placeholder="Enter narrative"></textarea>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Invoice No</mat-label>
        <input matInput formControlName="invoiceNo" placeholder="Enter invoice number">
        <mat-error *ngIf="hasError('invoiceNo')">
          {{ getErrorMessage('invoiceNo') }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Invoice Date *</mat-label>
        <input matInput [matDatepicker]="invoicePicker" formControlName="invoiceDate" readonly
          placeholder="Select invoice date" [max]="getTodayDate()">
        <mat-datepicker-toggle matSuffix [for]="invoicePicker"></mat-datepicker-toggle>
        <mat-datepicker #invoicePicker [startAt]="getTodayDate()"></mat-datepicker>
        <mat-hint>Invoice date can't be a future date</mat-hint>
        <mat-error *ngIf="hasError('invoiceDate')">
          {{ getErrorMessage('invoiceDate') }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Amount</mat-label>
        <input matInput type="number" formControlName="amount" placeholder="0.00" min="0" step="0.01">
        <mat-error *ngIf="hasError('amount')">
          {{ getErrorMessage('amount') }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Currency </mat-label>
        <mat-select formControlName="currency">
          <mat-option *ngFor="let currency of currencies" [value]="currency.value">
            {{ currency.symbol }} - {{ currency.name }}
          </mat-option>
        </mat-select>
        <mat-hint>Default: Nigerian Naira (₦)</mat-hint>
        <mat-error *ngIf="hasError('currency')">
          {{ getErrorMessage('currency') }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Bank Account *</mat-label>
        <mat-select formControlName="bankAccount">
          <mat-option value="">-- Select Bank Account --</mat-option>
          <mat-option *ngFor="let account of bankAccounts" [value]="account.value">
            {{ account.name }}
          </mat-option>
        </mat-select>
        <mat-hint>Choose the account to debit</mat-hint>
        <mat-error *ngIf="hasError('bankAccount')">
          {{ getErrorMessage('bankAccount') }}
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Type *</mat-label>
        <mat-select formControlName="type">
          <mat-option value="">-- Select Payee Type --</mat-option>
          <mat-option *ngFor="let type of payeeTypes" [value]="type.value">
            {{ type.name }}
          </mat-option>
        </mat-select>
        <mat-hint>Select the category of payee</mat-hint>
        <mat-error *ngIf="hasError('type')">
          {{ getErrorMessage('type') }}
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="half-width">
        <mat-label>Payment Option *</mat-label>
        <mat-select formControlName="paymentOption">
          <mat-option value="">-- Select Payment Method --</mat-option>
          <mat-optgroup label="Electronic Transfers">
            <mat-option *ngFor="let option of paymentOptions.slice(0, 3)" [value]="option.value">
              {{ option.name }}
            </mat-option>
          </mat-optgroup>
          <mat-optgroup label="Traditional Methods">
            <mat-option *ngFor="let option of paymentOptions.slice(3, 7)" [value]="option.value">
              {{ option.name }}
            </mat-option>
          </mat-optgroup>
          <mat-optgroup label="Digital & Mobile">
            <mat-option *ngFor="let option of paymentOptions.slice(7)" [value]="option.value">
              {{ option.name }}
            </mat-option>
          </mat-optgroup>
        </mat-select>
        <mat-hint>Choose the payment method</mat-hint>
        <mat-error *ngIf="hasError('paymentOption')">
          {{ getErrorMessage('paymentOption') }}
        </mat-error>
      </mat-form-field>
    </div>
  </form>


  <!-- Action Buttons -->
  <div class="action-buttons">
    <!-- <button mat-button >Clear</button> -->
    <button mat-raised-button color="primary" (click)="saveRequisition()" [disabled]="!isFormValid">Save</button>
  </div>
</div>