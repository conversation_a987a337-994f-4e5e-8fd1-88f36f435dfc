/* Page Container */
.create-requisitions-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h2 {
  margin: 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}


/* Form Structure */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

.half-width {
  width: calc(50% - 8px);
}

/* Material Form Fields */
.mat-mdc-form-field {
  width: 100%;
}

/* Create Payee */
.payee-option {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

.payee-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.payee-details {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.create-payee-option {
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.create-payee-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1976d2;
  font-weight: 500;
  padding: 4px 0;
  cursor: pointer;
}

.create-payee-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.create-payee-button:hover {
  background-color: #e3f2fd;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  justify-content: flex-end;
}

.action-buttons button {
  min-width: 160px;
  height: 45px;
  font-size: 14px;
  font-weight: 500;
  background-color: #900000;
  color: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: none;
  transition: all 0.2s ease;
}

.action-buttons button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.action-buttons button:disabled {
  background-color: #cccccc !important;
  color: #666666 !important;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

/* Form Styling */
.form-body {
  padding: 24px 32px;
  background-color: #fff;
  width: 95%;
  margin: 10px auto;
  border-radius: 8px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .half-width {
    width: 100%;
  }

  .header-section {
    flex-direction: column;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons button {
    width: 100%;
  }

  .form-body {
    padding: 20px 16px;
    width: 98%;
    margin: 10px auto;
  }
}

@media (max-width: 480px) {
  .title-section h2 {
    font-size: 22px;
  }

  .title-section p {
    font-size: 14px;
  }

  .action-buttons button {
    height: 40px;
    font-size: 13px;
  }

  .form-body {
    padding: 16px 12px;
  }
}
