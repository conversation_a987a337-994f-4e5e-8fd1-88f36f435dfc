import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  matSelectAnimations
} from "./chunk-GZI5T5RZ.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Mat<PERSON>refix,
  MatSuffix
} from "./chunk-U52GQYJB.js";
import "./chunk-GVVACSUB.js";
import "./chunk-TUJKHH33.js";
import "./chunk-2OG7VG7X.js";
import "./chunk-26JACEMX.js";
import "./chunk-HTZ36MZ2.js";
import "./chunk-7JBOIV3T.js";
import {
  Mat<PERSON>ptgroup,
  MatOption
} from "./chunk-UTYB2JNB.js";
import "./chunk-APQJ6POP.js";
import "./chunk-IGJZNA3K.js";
import "./chunk-V4GYEGQC.js";
import "./chunk-CONQKHOI.js";
import "./chunk-GC5FLHL6.js";
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
